# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
*.md
docs/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Elixir build artifacts
_build/
deps/
*.beam
*.ez

# Mix artifacts
.mix/

# Logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file (keep .env for Docker builds)
# .env

# Temporary files
tmp/
temp/

# Test artifacts
test_output/

# Docker files (except the ones we need)
Dockerfile*
docker-compose*.yml
!Dockerfile
!test/Dockerfile.test

# CI/CD files
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# Other
*.tar.gz
*.zip
