# Financial Indicator Daemon - Spring Boot 3 Implementation

This is a complete rewrite of the original Elixir/Phoenix financial indicator daemon in **Spring Boot 3** with **MyBatis
**, **PostgreSQL**, and **RabbitMQ**.

## Architecture Overview

The application processes cryptocurrency data through a pipeline:

1. **Data Mining**: Retrieves cryptocurrency mappings and historical data from CoinMarketCap API
2. **Data Transformation**: Calculates technical indicators using an external indicator API
3. **Statistics Generation**: Creates aggregated statistics from indicator data
4. **REST API**: Provides endpoints for retrieving statistics and monitoring

## Technology Stack

- **Java 17+** - Programming language
- **Spring Boot 3.2.5** - Application framework
- **MyBatis-Plus 3.5.9** - Database persistence framework with XML mappers
- **Spring Cloud OpenFeign** - Declarative HTTP clients
- **PostgreSQL 15** - Database
- **RabbitMQ 3** - Message broker
- **Liquibase** - Database migration tool
- **OpenAPI 3 (Swagger)** - API documentation
- **Gradle 8.10.2** - Build tool
- **Docker & Docker Compose** - Containerization

## Project Structure

```
src/
├── main/
│   ├── java/com/example/financialindicatordaemon/
│   │   ├── config/           # Configuration classes
│   │   ├── controller/       # REST controllers
│   │   ├── consumer/         # RabbitMQ message consumers
│   │   ├── dto/             # Data Transfer Objects
│   │   ├── entity/          # Database entities
│   │   ├── mapper/          # MyBatis mappers
│   │   └── service/         # Business logic services
│   └── resources/
│       ├── db/changelog/    # Liquibase migration scripts
│       └── application.yml  # Application configuration
└── test/                    # Test classes and resources
```

## Key Components

### Database Entities

- **Cryptocurrency**: Cryptocurrency metadata and mappings
- **CandleData**: Historical price and volume data
- **IndicatorData**: Technical indicator calculations
- **Statistics**: Aggregated statistical data

### RabbitMQ Consumers

- `mine_usd_data_by_symbols` - Processes USD-based cryptocurrency data
- `mine_btc_data_by_symbols` - Processes BTC-based cryptocurrency data
- `create_indicator_data` - Triggers indicator calculations
- `create_statistics` - Generates statistics
- `create_mappings` - Updates cryptocurrency mappings

### REST API Endpoints

- `GET /api/v1/crypto/statistics` - Retrieve cryptocurrency statistics
- `GET /monitoring/health` - Application health check
- `GET /docs` - Swagger UI documentation
- `GET /actuator/health` - Spring Boot actuator health endpoint

## Configuration

### Environment Variables

| Variable             | Description                 | Default                |
|----------------------|-----------------------------|------------------------|
| `POSTGRES_HOST`      | PostgreSQL host             | localhost              |
| `POSTGRES_PORT`      | PostgreSQL port             | 5432                   |
| `POSTGRES_DB`        | Database name               | financial_indicator_db |
| `POSTGRES_USER`      | Database username           | financial_user         |
| `POSTGRES_PASSWORD`  | Database password           | financial_pass         |
| `RABBITMQ_HOST`      | RabbitMQ host               | localhost              |
| `RABBITMQ_PORT`      | RabbitMQ port               | 5672                   |
| `RABBITMQ_USER`      | RabbitMQ username           | guest                  |
| `RABBITMQ_PASSWORD`  | RabbitMQ password           | guest                  |
| `CMC_API_KEY`        | CoinMarketCap API key       | -                      |
| `INDICATOR_API_HOST` | External indicator API host | http://localhost:5000  |

## Running the Application

### Using Docker Compose (Recommended)

```bash
# Build and start all services
docker-compose -f docker-compose.springboot.yml up --build

# Start in detached mode
docker-compose -f docker-compose.springboot.yml up -d --build

# View logs
docker-compose -f docker-compose.springboot.yml logs -f financial-indicator-daemon

# Stop services
docker-compose -f docker-compose.springboot.yml down
```

### Local Development

1. **Prerequisites**:
    - Java 17+
    - Gradle 8.10.2+ (or use included wrapper)
    - PostgreSQL 15+
    - RabbitMQ 3+

2. **Database Setup**:
   ```bash
   # Create database and user
   createdb financial_indicator_db
   createuser financial_user

   # Run Liquibase migrations
   ./gradlew liquibaseUpdate
   ```

3. **Run Application**:
   ```bash
   ./gradlew bootRun
   ```

## API Documentation

Once the application is running, access the Swagger UI at:

- http://localhost:6601/docs

The OpenAPI specification is available at:

- http://localhost:6601/docs/openapi.json

## Testing

```bash
# Run all tests
./gradlew test

# Run tests with coverage
./gradlew test jacocoTestReport

# Run integration tests
./gradlew integrationTest

# Build and test everything
./gradlew build
```

## Monitoring

### Health Checks

- Application: http://localhost:6601/actuator/health
- Custom monitoring: http://localhost:6601/monitoring/health

### Management Endpoints

- Metrics: http://localhost:6601/actuator/metrics
- Info: http://localhost:6601/actuator/info

## Migration from Elixir

### Key Differences

1. **Language**: Elixir → Java 17+
2. **Framework**: Phoenix → Spring Boot 3
3. **ORM**: Ecto → MyBatis-Plus with XML mappers
4. **HTTP Clients**: HTTPoison → Spring Cloud OpenFeign
5. **Build Tool**: Mix → Gradle
6. **Configuration**: Elixir config → Spring Boot YAML
7. **Dependency Injection**: Manual → Spring IoC

### Preserved Features

- ✅ Same database schema (Liquibase migrations)
- ✅ Same RabbitMQ queue structure
- ✅ Same REST API endpoints and responses
- ✅ Same business logic and data processing
- ✅ Same external API integrations

### Performance Considerations

- **Memory**: Java typically uses more memory than Elixir
- **Startup**: Spring Boot has longer startup time
- **Throughput**: Comparable performance for this use case
- **Concurrency**: Spring Boot's thread-per-request vs Elixir's actor model

## Development Notes

### Code Style

- Follow Spring Boot best practices
- Use constructor injection over field injection
- Prefer immutable DTOs and configuration classes
- Use proper logging with SLF4J

### Database Access

- MyBatis-Plus provides JPA-like annotations with SQL flexibility
- Raw SQL queries are preserved in mapper interfaces
- Automatic CRUD operations with BaseMapper

### Error Handling

- Global exception handling with @ControllerAdvice
- Proper HTTP status codes
- Structured error responses

## Future Enhancements

- [ ] Add comprehensive integration tests
- [ ] Implement CoinMarketCap API client
- [ ] Add metrics and monitoring with Micrometer
- [ ] Implement caching with Redis
- [ ] Add API rate limiting
- [ ] Implement async processing with @Async
- [ ] Add security with Spring Security
