# Financial Indicator Daemon

Cryptocurrency data processing API with automated pipeline.

## Deployment

### Environment Variables

```bash
CMC_API_KEY=your_coinmarketcap_api_key
RABBITMQ_HOST=rabbitmq_host
RABBITMQ_PORT=5672
```

### Docker Compose

```bash
# Start API and RabbitMQ
docker-compose up --build -d

# Start with automated pipeline (cron)
docker-compose --profile cron up -d

# Health check
curl http://localhost:6601/monitoring/health
```

### Cloud Deployment

1. Set environment variables in your cloud provider
2. Deploy using docker-compose or container orchestration
3. Ensure RabbitMQ persistence and API accessibility
4. Configure load balancer for port 6601

## Pipeline Configuration

Cryptocurrency symbols are configured in `cron/crontab`:
```bash
# Edit symbols (single source of truth)
5 */6 * * * python mine_usd_data_by_symbols.py --symbols BTC ETH ADA SOL DOT
```

Rebuild after changes:
```bash
docker-compose --profile cron build pipeline-cron
```

## API Endpoints

- `/docs/` - Swagger documentation
- `/api/v1/crypto/statistics` - Cryptocurrency statistics
- `/monitoring/health` - Health check

## Testing

```bash
cd test && docker-compose -f docker-compose.test.yml up --build
```
