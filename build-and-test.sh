#!/bin/bash

set -e

echo "🚀 Building and Testing Financial Indicator Daemon (Spring Boot)"
echo "================================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Java 17+ is available
echo "🔍 Checking Java version..."
if command -v java &> /dev/null; then
    JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
    if [ "$JAVA_VERSION" -ge 17 ]; then
        print_status "Java $JAVA_VERSION found"
    else
        print_error "Java 17+ required, found Java $JAVA_VERSION"
        exit 1
    fi
else
    print_error "Java not found. Please install Java 17+"
    exit 1
fi

# Check if Gradle wrapper is available
echo "🔍 Checking Gradle..."
if [ -f "./gradlew" ]; then
    print_status "Gradle wrapper found"
else
    print_error "Gradle wrapper not found. Please ensure gradlew is present"
    exit 1
fi

# Clean and compile
echo "🧹 Cleaning and compiling..."
./gradlew clean compileJava
print_status "Compilation successful"

# Run tests
echo "🧪 Running tests..."
./gradlew test
print_status "Tests passed"

# Package application
echo "📦 Packaging application..."
./gradlew build -x test
print_status "Packaging successful"

# Check if Docker is available for container build
if command -v docker &> /dev/null; then
    echo "🐳 Building Docker image..."
    docker build -f Dockerfile.springboot -t financial-indicator-daemon:latest .
    print_status "Docker image built successfully"
else
    print_warning "Docker not found. Skipping Docker image build"
fi

echo ""
echo "🎉 Build completed successfully!"
echo ""
echo "📋 Next steps:"
echo "   • Run locally: ./gradlew bootRun"
echo "   • Run with Docker: docker-compose -f docker-compose.springboot.yml up"
echo "   • Access API docs: http://localhost:6601/docs"
echo "   • Health check: http://localhost:6601/actuator/health"
echo ""
