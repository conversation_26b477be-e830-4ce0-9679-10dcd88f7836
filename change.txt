diff --git a/config/config.exs b/config/config.exs
index 2cb8ba9..965ff70 100644
--- a/config/config.exs
+++ b/config/config.exs
@@ -1,5 +1,16 @@
 import Config

+config :finance_indicator_daemon,
+  ecto_repos: [FinanceIndicatorDaemon.Repo]
+
+config :finance_indicator_daemon, FinanceIndicatorDaemon.Repo,
+  database: System.get_env("POSTGRES_DB", "financial_indicator_db"),
+  username: System.get_env("POSTGRES_USER", "financial_user"),
+  password: System.get_env("POSTGRES_PASSWORD", "financial_pass"),
+  hostname: System.get_env("POSTGRES_HOST", "postgres"),
+  port: String.to_integer(System.get_env("POSTGRES_PORT", "5432")),
+  pool_size: 10
+
 config :finance_indicator_daemon, FinanceIndicatorDaemon.Endpoint,
   http: [port: 6601],
   secret_key_base: "secret_key_base",
diff --git a/docker-compose.yml b/docker-compose.yml
index 71fac36..8a89061 100644
--- a/docker-compose.yml
+++ b/docker-compose.yml
@@ -51,8 +51,7 @@ services:
       - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
       - CMC_API_THROTTLE_MIN=${CMC_API_THROTTLE_MIN}
       - CMC_API_THROTTLE_MAX=${CMC_API_THROTTLE_MAX}
-    volumes:
-      - ./out:/app/out
+
     ports:
       - "6601:6601"

diff --git a/lib/api/indicator_api.ex b/lib/api/indicator_api.ex
index 3e31548..9e34e22 100644
--- a/lib/api/indicator_api.ex
+++ b/lib/api/indicator_api.ex
@@ -30,6 +30,7 @@ defmodule Api.IndicatorApi do
     json_data = if is_binary(data), do: data, else: Jason.encode!(data)
     data_size = byte_size(json_data)
     Logger.info("📤 Sending POST request to: #{url} (payload: #{data_size} bytes)")
+    Logger.debug("🔍 Request payload sample: #{String.slice(json_data, 0, 200)}...")

     headers = [{"Content-Type", "application/json"}]
     start_time = System.monotonic_time(:millisecond)
@@ -39,6 +40,7 @@ defmodule Api.IndicatorApi do
         response_time = System.monotonic_time(:millisecond) - start_time
         response_size = byte_size(body)
         Logger.info("✅ POST request successful in #{response_time}ms (response: #{response_size} bytes)")
+        Logger.debug("🔍 Response sample: #{String.slice(body, 0, 300)}...")
         {:ok, body}
       {:ok, %HTTPoison.Response{status_code: status_code, body: body}} ->
         response_time = System.monotonic_time(:millisecond) - start_time
diff --git a/lib/app_config.ex b/lib/app_config.ex
index 47a3232..21f7f3d 100644
--- a/lib/app_config.ex
+++ b/lib/app_config.ex
@@ -3,60 +3,14 @@ defmodule AppConfig do

   def get_variable(name, default \\ nil), do: System.get_env(name, default)

-  defp ensure_path(path) do
-    if path do
-      case File.mkdir_p(path) do
-        :ok ->
-          require Logger
-          Logger.debug("Created directory: #{path}")
-        {:error, reason} ->
-          require Logger
-          Logger.error("Failed to create directory #{path}: #{inspect(reason)}")
-      end
-    end
-    path
-  end
-
-  def get_base_app_path do
-    get_variable("BASE_APP_PATH", "./out") |> ensure_path()
-  end
-
-  def get_mappings_out_path do
-    get_variable("MAPPINGS_OUT_PATH", "./out/mappings") |> ensure_path()
-  end
-
-  def get_base_candle_data_out_path do
-    get_variable("USD_BASE_CANDLE_DATA_OUT_PATH", "./out/candle_data/usd") |> ensure_path()
-  end
-
-  def get_btc_base_candle_data_out_path do
-    get_variable("BTC_BASE_CANDLE_DATA_OUT_PATH", "./out/candle_data/btc") |> ensure_path()
-  end
-
   def get_indicator_api_host do
     get_variable("INDICATOR_API_HOST", "http://localhost:6501")
   end

-  def get_indicator_data_out_path do
-    get_variable("USD_INDICATOR_DATA_OUT_PATH", "./out/indicator_data/usd") |> ensure_path()
-  end
-
-  def get_btc_indicator_data_out_path do
-    get_variable("BTC_INDICATOR_DATA_OUT_PATH", "./out/indicator_data/btc") |> ensure_path()
-  end
-
   def get_amount_of_coins_to_get_by_ranking do
     get_variable("AMOUNT_OF_COINS_TO_GET_BY_RANKING", "10") |> String.to_integer()
   end

-  def get_statistics_out_path do
-    get_variable("USD_STATISTICS_OUT_PATH", "./out/statistics/usd") |> ensure_path()
-  end
-
-  def get_btc_statistics_out_path do
-    get_variable("BTC_STATISTICS_OUT_PATH", "./out/statistics/btc") |> ensure_path()
-  end
-
   def get_rabbitmq_host, do: get_variable("RABBITMQ_HOST")

   def get_rabbitmq_port do
diff --git a/lib/coinmarketcap/api/coinmarketcap_client.ex b/lib/coinmarketcap/api/coinmarketcap_client.ex
index 747bca8..4db83ee 100644
--- a/lib/coinmarketcap/api/coinmarketcap_client.ex
+++ b/lib/coinmarketcap/api/coinmarketcap_client.ex
@@ -63,14 +63,23 @@ defmodule Coinmarketcap.Api.CoinmarketcapClient do

       case send_get_request(url, headers, []) do
         {:ok, body} ->
+          Logger.debug("🔍 Parsing JSON response (size: #{byte_size(body)} bytes)")
           json = Jason.decode!(body)
+          Logger.debug("🔍 JSON structure keys: #{inspect(Map.keys(json))}")
+
           quotes = @json_utils.get_json_value(json, "data.quotes")
-          quotes_count = length(quotes)
+          Logger.debug("🔍 Quotes value: #{inspect(quotes)} (type: #{if quotes, do: "#{length(quotes)} items", else: "nil"})")

+          quotes_count = if quotes, do: length(quotes), else: 0
           Logger.info("📈 Received #{quotes_count} data points from API")

           if quotes_count < 1 do
-            total_records = accumulator |> Enum.map(&length(@json_utils.get_json_value(&1, "data.quotes"))) |> Enum.sum()
+            total_records = accumulator
+              |> Enum.map(fn item ->
+                quotes = @json_utils.get_json_value(item, "data.quotes")
+                if quotes, do: length(quotes), else: 0
+              end)
+              |> Enum.sum()
             Logger.info("✅ API pagination complete, total records collected: #{total_records}")
             {:ok, convert_accumulator_to_data(accumulator)}
           else
@@ -85,18 +94,56 @@ defmodule Coinmarketcap.Api.CoinmarketcapClient do
   end

   def convert_accumulator_to_data(accumulator) do
-    accumulator
+    Logger.debug("🔄 Converting accumulator to data (#{length(accumulator)} items)")
+
+    data_items = accumulator
     |> Enum.map(&@json_utils.get_json_value(&1, "data"))
+    |> Enum.filter(& &1)
+
+    Logger.debug("🔍 Data items after filtering: #{length(data_items)}")
+
+    quotes_items = data_items
     |> Enum.map(&@json_utils.get_json_value(&1, "quotes"))
-    |> Enum.flat_map(& &1)
+    |> Enum.filter(& &1)
+
+    Logger.debug("🔍 Quotes items after filtering: #{length(quotes_items)}")
+
+    flat_quotes = quotes_items |> Enum.flat_map(& &1)
+    Logger.debug("🔍 Flat quotes count: #{length(flat_quotes)}")
+
+    quote_items = flat_quotes
     |> Enum.map(&@json_utils.get_json_value(&1, "quote"))
-    |> Enum.sort(&compare_timestamps/2)
+    |> Enum.filter(& &1)
+
+    Logger.debug("🔍 Quote items after filtering: #{length(quote_items)}")
+
+    if length(quote_items) > 0 do
+      Logger.debug("🔍 First quote item structure: #{inspect(Enum.take(quote_items, 1))}")
+    end
+
+    sorted_items = quote_items |> Enum.sort(&compare_timestamps/2)
+    Logger.debug("✅ Final sorted items count: #{length(sorted_items)}")
+
+    sorted_items
   end

   defp compare_timestamps(x, y) do
-    {:ok, datetime_x, _} = DateTime.from_iso8601(@json_utils.get_json_value(x, "timestamp"))
-    {:ok, datetime_y, _} = DateTime.from_iso8601(@json_utils.get_json_value(y, "timestamp"))
-    DateTime.compare(datetime_x, datetime_y) == :lt
+    timestamp_x = @json_utils.get_json_value(x, "timestamp")
+    timestamp_y = @json_utils.get_json_value(y, "timestamp")
+
+    case {DateTime.from_iso8601(timestamp_x), DateTime.from_iso8601(timestamp_y)} do
+      {{:ok, datetime_x, _}, {:ok, datetime_y, _}} ->
+        DateTime.compare(datetime_x, datetime_y) == :lt
+      {{:error, reason_x}, _} ->
+        Logger.error("❌ Failed to parse timestamp X: #{timestamp_x}, reason: #{inspect(reason_x)}")
+        false
+      {_, {:error, reason_y}} ->
+        Logger.error("❌ Failed to parse timestamp Y: #{timestamp_y}, reason: #{inspect(reason_y)}")
+        true
+      _ ->
+        Logger.error("❌ Unexpected timestamp parsing result for X: #{timestamp_x}, Y: #{timestamp_y}")
+        false
+    end
   end

   defp send_get_request(url, headers, params) do
diff --git a/lib/database_service.ex b/lib/database_service.ex
index 439204b..cd4e791 100644
--- a/lib/database_service.ex
+++ b/lib/database_service.ex
@@ -3,15 +3,72 @@ defmodule FinanceIndicatorDaemon.DatabaseService do
   alias FinanceIndicatorDaemon.Repo
   require Logger

+  defmodule Cryptocurrency do
+    use Ecto.Schema
+    @schema_prefix "financial_data"
+    schema "cryptocurrencies" do
+      field :symbol, :string
+      field :name, :string
+      field :slug, :string
+      field :rank, :integer
+      field :is_active, :boolean
+      field :first_historical_data, :naive_datetime
+      field :last_historical_data, :naive_datetime
+      field :platform, :map
+      timestamps(inserted_at: :created_at)
+    end
+  end
+
+  defmodule CandleData do
+    use Ecto.Schema
+    @schema_prefix "financial_data"
+    schema "candle_data" do
+      field :cryptocurrency_id, :integer
+      field :timestamp, :naive_datetime
+      field :currency_type, :string
+      field :open_price, :decimal
+      field :high_price, :decimal
+      field :low_price, :decimal
+      field :close_price, :decimal
+      field :volume, :decimal
+      field :market_cap, :decimal
+      timestamps(inserted_at: :created_at, updated_at: false)
+    end
+  end
+
+  defmodule IndicatorData do
+    use Ecto.Schema
+    @schema_prefix "financial_data"
+    schema "indicator_data" do
+      field :cryptocurrency_id, :integer
+      field :timestamp, :naive_datetime
+      field :currency_type, :string
+      field :indicator_values, :map
+      timestamps(inserted_at: :created_at, updated_at: false)
+    end
+  end
+
+  defmodule Statistics do
+    use Ecto.Schema
+    @schema_prefix "financial_data"
+    schema "statistics" do
+      field :cryptocurrency_id, :integer
+      field :date_calculated, :date
+      field :currency_type, :string
+      field :statistics_data, :map
+      timestamps(inserted_at: :created_at, updated_at: false)
+    end
+  end
+
   # Cryptocurrency mappings
   def do_mappings_exist() do
-    Repo.one(from "crypto_data.cmc_mappings", select: count()) > 0
+    Repo.one(from c in Cryptocurrency, select: count(c.id)) > 0
   end

   def write_mappings(mappings_json) do
     %{"data" => data} = Jason.decode!(mappings_json)
     Logger.info("Writing #{length(data)} cryptocurrency mappings to database")
-
+
     records = Enum.map(data, fn m ->
       %{
         id: m["id"],
@@ -23,45 +80,60 @@ defmodule FinanceIndicatorDaemon.DatabaseService do
         first_historical_data: parse_datetime(m["first_historical_data"]),
         last_historical_data: parse_datetime(m["last_historical_data"]),
         platform: m["platform"],
-        inserted_at: NaiveDateTime.utc_now(),
-        updated_at: NaiveDateTime.utc_now()
+        created_at: NaiveDateTime.utc_now() |> NaiveDateTime.truncate(:second),
+        updated_at: NaiveDateTime.utc_now() |> NaiveDateTime.truncate(:second)
       }
     end)
-
-    Repo.insert_all("crypto_data.cmc_mappings", records,
-      on_conflict: {:replace_all_except, [:inserted_at]},
-      conflict_target: :id
-    )
+
+    # Deduplicate by symbol (keep the first occurrence) and insert in batches
+    records
+    |> Enum.uniq_by(& &1.symbol)
+    |> Enum.chunk_every(1000)
+    |> Enum.each(fn batch ->
+      Repo.insert_all(Cryptocurrency, batch,
+        on_conflict: {:replace_all_except, [:created_at]},
+        conflict_target: :symbol
+      )
+    end)
   end

   def get_coin_id(symbol) do
-    query = from "crypto_data.cmc_mappings",
-      where: [symbol: ^symbol],
-      select: [:id]
-
+    query = from c in Cryptocurrency,
+      where: c.symbol == ^symbol,
+      select: c.id
+
     case Repo.one(query) do
-      %{id: id} -> id
+      id when is_integer(id) -> id
       nil -> nil
     end
   end

   # Candle data
   def does_coin_data_exist(coin_id, date_string, currency) do
+    Logger.debug("🔍 Checking if coin data exists: coin_id=#{coin_id}, date_string='#{date_string}', currency=#{currency}")
+
     currency_str = String.upcase(to_string(currency))
-    {:ok, date} = Date.from_iso8601(date_string)
-
-    query = from "crypto_data.cmc_candle_data",
-      where: [cryptocurrency_id: ^coin_id, currency_type: ^currency_str],
-      where: fragment("date(timestamp) = ?", ^date),
-      select: count()
-
-    Repo.one(query) > 0
+    Logger.debug("🔄 Currency converted to: '#{currency_str}'")
+
+    date = parse_date_string(date_string)
+    Logger.debug("🔄 Date parsed to: #{Date.to_iso8601(date)}")
+
+    query = from cd in CandleData,
+      where: cd.cryptocurrency_id == ^coin_id and cd.currency_type == ^currency_str,
+      where: fragment("date(?)", cd.timestamp) == ^date,
+      select: count(cd.id)
+
+    count = Repo.one(query)
+    exists = count > 0
+    Logger.debug("📊 Query result: count=#{count}, exists=#{exists}")
+
+    exists
   end

   def write_coin_data(coin_id, _date_string, data, currency) do
     currency_str = String.upcase(to_string(currency))
     Logger.info("💾 Writing #{length(data)} candle data records")
-
+
     records = Enum.map(data, fn d ->
       %{
         cryptocurrency_id: coin_id,
@@ -72,25 +144,25 @@ defmodule FinanceIndicatorDaemon.DatabaseService do
         low_price: d["low"],
         close_price: d["close"],
         volume: d["volume"],
-        market_cap: d["market_cap"],
-        inserted_at: NaiveDateTime.utc_now()
+        market_cap: d["marketCap"],
+        created_at: NaiveDateTime.utc_now() |> NaiveDateTime.truncate(:second)
       }
     end)
-
-    Repo.insert_all("crypto_data.cmc_candle_data", records,
-      on_conflict: {:replace_all_except, [:id, :inserted_at]},
+
+    Repo.insert_all(CandleData, records,
+      on_conflict: {:replace_all_except, [:id, :created_at]},
       conflict_target: [:cryptocurrency_id, :timestamp, :currency_type]
     )
   end

   def find_latest_coin_data(coin_id, currency) do
     currency_str = String.upcase(to_string(currency))
-
-    query = from "crypto_data.cmc_candle_data",
-      where: [cryptocurrency_id: ^coin_id, currency_type: ^currency_str],
-      order_by: [desc: :timestamp],
-      select: [:timestamp, :open_price, :high_price, :low_price, :close_price, :volume, :market_cap]
-
+
+    query = from cd in CandleData,
+      where: cd.cryptocurrency_id == ^coin_id and cd.currency_type == ^currency_str,
+      order_by: [desc: cd.timestamp],
+      select: cd
+
     Repo.all(query)
     |> Enum.map(fn r ->
       %{
@@ -100,37 +172,57 @@ defmodule FinanceIndicatorDaemon.DatabaseService do
         "low" => decimal_to_float(r.low_price),
         "close" => decimal_to_float(r.close_price),
         "volume" => decimal_to_float(r.volume),
-        "market_cap" => decimal_to_float(r.market_cap)
+        "marketCap" => decimal_to_float(r.market_cap)
       }
     end)
   end

   def read_all_coins_data_file_names(currency) do
     currency_str = String.upcase(to_string(currency))
-
-    query = from "crypto_data.cmc_candle_data",
-      where: [currency_type: ^currency_str],
-      select: [:cryptocurrency_id, fragment("date(timestamp)")],
+
+    query = from cd in CandleData,
+      where: cd.currency_type == ^currency_str,
+      select: cd.cryptocurrency_id,
       distinct: true
-
+
     Repo.all(query)
-    |> Enum.map(fn %{cryptocurrency_id: id, date: date} ->
-      "#{String.downcase(currency_str)}_data_for_all_time-#{id}-#{Date.to_iso8601(date)}.json"
+    |> Enum.map(fn id ->
+      "#{String.downcase(currency_str)}_data_for_all_time-#{id}-all.json"
     end)
   end

   def read_coin_data(file_name, currency) do
     case parse_filename(file_name) do
+      {coin_id, "all"} ->
+        currency_str = String.upcase(to_string(currency))
+
+        query = from cd in CandleData,
+          where: cd.cryptocurrency_id == ^coin_id and cd.currency_type == ^currency_str,
+          order_by: [asc: cd.timestamp],
+          select: cd
+
+        Repo.all(query)
+        |> Enum.map(fn r ->
+          %{
+            "timestamp" => NaiveDateTime.to_iso8601(r.timestamp),
+            "open" => decimal_to_float(r.open_price),
+            "high" => decimal_to_float(r.high_price),
+            "low" => decimal_to_float(r.low_price),
+            "close" => decimal_to_float(r.close_price),
+            "volume" => decimal_to_float(r.volume),
+            "marketCap" => decimal_to_float(r.market_cap)
+          }
+        end)
       {coin_id, date_string} ->
         currency_str = String.upcase(to_string(currency))
-        {:ok, date} = Date.from_iso8601(date_string)
-
-        query = from "crypto_data.cmc_candle_data",
-          where: [cryptocurrency_id: ^coin_id, currency_type: ^currency_str],
-          where: fragment("date(timestamp) = ?", ^date),
-          order_by: [asc: :timestamp],
-          select: [:timestamp, :open_price, :high_price, :low_price, :close_price, :volume, :market_cap]
-
+        date = parse_date_string(date_string)
+
+        query = from cd in CandleData,
+          where: cd.cryptocurrency_id == ^coin_id and cd.currency_type == ^currency_str,
+          where: fragment("date(?)", cd.timestamp) == ^date,
+          order_by: [asc: cd.timestamp],
+          select: cd
+
         Repo.all(query)
         |> Enum.map(fn r ->
           %{
@@ -140,7 +232,7 @@ defmodule FinanceIndicatorDaemon.DatabaseService do
             "low" => decimal_to_float(r.low_price),
             "close" => decimal_to_float(r.close_price),
             "volume" => decimal_to_float(r.volume),
-            "market_cap" => decimal_to_float(r.market_cap)
+            "marketCap" => decimal_to_float(r.market_cap)
           }
         end)
       nil -> []
@@ -150,39 +242,72 @@ defmodule FinanceIndicatorDaemon.DatabaseService do
   # Indicator data
   def write_coin_indicator_data(data, file_name, currency) do
     case parse_filename(file_name) do
-      {coin_id, date_string} ->
+      {coin_id, _date_string} ->
         currency_str = String.upcase(to_string(currency))
-        {:ok, parsed} = Jason.decode(data)
+        cleaned_data = String.replace(data, "NaN", "null")
+        {:ok, parsed} = Jason.decode(cleaned_data)
         indicators = if is_list(parsed), do: parsed, else: [parsed]
-
-        records = Enum.map(indicators, fn i ->
+
+        Logger.info("📊 Processing #{length(indicators)} indicator records")
+
+        valid_records = indicators
+        |> Enum.with_index()
+        |> Enum.map(fn {i, idx} ->
+          Logger.debug("🔍 Record #{idx}: timestamp=\"#{i["timestamp"]}\", has_timestamp=#{i["timestamp"] != nil}")
+          {i, idx}
+        end)
+        |> Enum.map(fn {i, idx} ->
+          parsed_timestamp = parse_datetime(i["timestamp"])
+          Logger.debug("🔄 Record #{idx}: parsed_timestamp=#{inspect(parsed_timestamp)}")
+          {i, parsed_timestamp}
+        end)
+        |> Enum.filter(fn {i, parsed_timestamp} ->
+          valid = parsed_timestamp != nil
+          Logger.debug("🔍 Final record: timestamp=#{inspect(parsed_timestamp)}, valid=#{valid}")
+          valid
+        end)
+        |> Enum.map(fn {i, parsed_timestamp} ->
           %{
             cryptocurrency_id: coin_id,
-            timestamp: parse_datetime(i["timestamp"]),
+            timestamp: parsed_timestamp,
             currency_type: currency_str,
             indicator_values: Map.delete(i, "timestamp"),
-            inserted_at: NaiveDateTime.utc_now()
+            created_at: NaiveDateTime.utc_now() |> NaiveDateTime.truncate(:second)
           }
         end)
-
-        Repo.insert_all("crypto_data.indicator_data", records,
-          on_conflict: {:replace_all_except, [:id, :inserted_at]},
-          conflict_target: [:cryptocurrency_id, :timestamp, :currency_type]
-        )
+
+        Logger.info("📊 Filtered to #{length(valid_records)} valid records for database insertion")
+
+        if length(valid_records) > 0 do
+          Repo.insert_all(IndicatorData, valid_records,
+            on_conflict: {:replace_all_except, [:id, :created_at]},
+            conflict_target: [:cryptocurrency_id, :timestamp, :currency_type]
+          )
+        else
+          Logger.warn("⚠️  No valid records to insert for #{file_name}")
+        end
       nil -> {:error, "Invalid filename"}
     end
   end

   def read_coin_indicator_data(coin_id, date_string, currency) do
     currency_str = String.upcase(to_string(currency))
-    {:ok, date} = Date.from_iso8601(date_string)
-
-    query = from "crypto_data.indicator_data",
-      where: [cryptocurrency_id: ^coin_id, currency_type: ^currency_str],
-      where: fragment("date(timestamp) = ?", ^date),
-      order_by: [asc: :timestamp],
-      select: [:timestamp, :indicator_values]
-
+
+    query = if date_string == "all" do
+      from id in IndicatorData,
+        where: id.cryptocurrency_id == ^coin_id and id.currency_type == ^currency_str,
+        order_by: [desc: id.timestamp],
+        limit: 1,
+        select: id
+    else
+      date = parse_date_string(date_string)
+      from id in IndicatorData,
+        where: id.cryptocurrency_id == ^coin_id and id.currency_type == ^currency_str,
+        where: fragment("date(?)", id.timestamp) == ^date,
+        order_by: [asc: id.timestamp],
+        select: id
+    end
+
     Repo.all(query)
     |> Enum.map(fn r ->
       Map.put(r.indicator_values, "timestamp", NaiveDateTime.to_iso8601(r.timestamp))
@@ -191,72 +316,98 @@ defmodule FinanceIndicatorDaemon.DatabaseService do

   def read_all_coins_indicator_data_file_names(currency) do
     currency_str = String.upcase(to_string(currency))
-
-    query = from "crypto_data.indicator_data",
-      where: [currency_type: ^currency_str],
-      select: [:cryptocurrency_id, fragment("date(timestamp)")],
+
+    query = from id in IndicatorData,
+      where: id.currency_type == ^currency_str,
+      select: id.cryptocurrency_id,
       distinct: true
-
+
     Repo.all(query)
-    |> Enum.map(fn %{cryptocurrency_id: id, date: date} ->
-      "#{String.downcase(currency_str)}_data_for_all_time-#{id}-#{Date.to_iso8601(date)}.json"
+    |> Enum.map(fn id ->
+      "#{String.downcase(currency_str)}_data_for_all_time-#{id}-all.json"
     end)
   end

   # Statistics
   def write_statistics(data, coin_id, date_string, currency) do
     currency_str = String.upcase(to_string(currency))
-    {:ok, date} = Date.from_iso8601(date_string)
-
+    date = parse_date_string(date_string)
+
     record = %{
       cryptocurrency_id: coin_id,
       date_calculated: date,
       currency_type: currency_str,
       statistics_data: data,
-      inserted_at: NaiveDateTime.utc_now()
+      created_at: NaiveDateTime.utc_now() |> NaiveDateTime.truncate(:second)
     }
-
-    Repo.insert_all("crypto_data.statistics", [record],
-      on_conflict: {:replace_all_except, [:id, :inserted_at]},
+
+    Repo.insert_all(Statistics, [record],
+      on_conflict: {:replace_all_except, [:id, :created_at]},
       conflict_target: [:cryptocurrency_id, :date_calculated, :currency_type]
     )
   end

   def read_all_latest_statistics(currency) do
     currency_str = String.upcase(to_string(currency))
-
-    query = from s in "crypto_data.statistics",
+
+    query = from s in Statistics,
       where: s.currency_type == ^currency_str,
       group_by: s.cryptocurrency_id,
       select: %{
         cryptocurrency_id: s.cryptocurrency_id,
         max_date: max(s.date_calculated)
       }
-
+
     subquery_results = Repo.all(query)
-
+
     Enum.flat_map(subquery_results, fn %{cryptocurrency_id: id, max_date: date} ->
-      query = from "crypto_data.statistics",
-        where: [cryptocurrency_id: ^id, date_calculated: ^date, currency_type: ^currency_str],
-        select: [:statistics_data]
-
-      Repo.all(query) |> Enum.map(& &1.statistics_data)
+      query = from s in Statistics,
+        where: s.cryptocurrency_id == ^id and s.date_calculated == ^date and s.currency_type == ^currency_str,
+        select: s.statistics_data
+
+      Repo.all(query)
     end)
   end

   def list_files() do
-    query = from "crypto_data.statistics",
-      select: [fragment("concat(currency_type, '_stats_', cryptocurrency_id)")],
+    query = from s in Statistics,
+      select: fragment("concat(?, '_stats_', ?)", s.currency_type, s.cryptocurrency_id),
       limit: 5
-
-    Repo.all(query) |> Enum.map(&List.first/1)
+
+    Repo.all(query)
+  end
+
+  def read_mappings() do
+    query = from c in Cryptocurrency,
+      select: c
+
+    data = Repo.all(query)
+    |> Enum.map(fn c ->
+      %{
+        "id" => c.id,
+        "symbol" => c.symbol,
+        "name" => c.name,
+        "slug" => c.slug,
+        "rank" => c.rank,
+        "is_active" => if(c.is_active, do: 1, else: 0),
+        "first_historical_data" => if(c.first_historical_data, do: NaiveDateTime.to_iso8601(c.first_historical_data)),
+        "last_historical_data" => if(c.last_historical_data, do: NaiveDateTime.to_iso8601(c.last_historical_data)),
+        "platform" => c.platform
+      }
+    end)
+
+    %{"data" => data}
+  end
+
+  def delete_mappings() do
+    Repo.delete_all(Cryptocurrency)
   end

   # Helpers
   defp parse_datetime(nil), do: nil
   defp parse_datetime(str) when is_binary(str) do
     case NaiveDateTime.from_iso8601(str) do
-      {:ok, dt} -> dt
+      {:ok, dt} -> NaiveDateTime.truncate(dt, :second)
       _ -> nil
     end
   end
@@ -267,8 +418,47 @@ defmodule FinanceIndicatorDaemon.DatabaseService do

   defp parse_filename(file_name) do
     case Regex.run(~r/^[a-z]+_data_for_all_time-(\d+)-(.+)\.json$/, file_name) do
+      [_, id_str, "all"] -> {String.to_integer(id_str), "all"}
       [_, id_str, date_str] -> {String.to_integer(id_str), date_str}
       _ -> nil
     end
   end
+
+  defp parse_date_string(date_string) do
+    Logger.debug("🔍 Parsing date string: '#{date_string}' (length: #{String.length(date_string)})")
+
+    case String.length(date_string) do
+      8 ->
+        # Format: "20250616" -> "2025-06-16"
+        year = String.slice(date_string, 0, 4)
+        month = String.slice(date_string, 4, 2)
+        day = String.slice(date_string, 6, 2)
+        iso_date = "#{year}-#{month}-#{day}"
+        Logger.debug("🔄 Converting '#{date_string}' to ISO format: '#{iso_date}'")
+
+        case Date.from_iso8601(iso_date) do
+          {:ok, date} ->
+            Logger.debug("✅ Successfully parsed date: #{Date.to_iso8601(date)}")
+            date
+          {:error, reason} ->
+            Logger.error("❌ Failed to parse date string '#{date_string}' -> '#{iso_date}': #{inspect(reason)}")
+            raise "Invalid date format: #{date_string}"
+        end
+      10 ->
+        # Format: "2025-06-16"
+        Logger.debug("🔄 Parsing ISO date format: '#{date_string}'")
+
+        case Date.from_iso8601(date_string) do
+          {:ok, date} ->
+            Logger.debug("✅ Successfully parsed ISO date: #{Date.to_iso8601(date)}")
+            date
+          {:error, reason} ->
+            Logger.error("❌ Failed to parse date string '#{date_string}': #{inspect(reason)}")
+            raise "Invalid date format: #{date_string}"
+        end
+      _ ->
+        Logger.error("❌ Unsupported date string length: '#{date_string}' (length: #{String.length(date_string)})")
+        raise "Invalid date format: #{date_string}"
+    end
+  end
 end
diff --git a/lib/execution/statistics.ex b/lib/execution/statistics.ex
index 44556bc..f53092c 100644
--- a/lib/execution/statistics.ex
+++ b/lib/execution/statistics.ex
@@ -1,5 +1,5 @@
 defmodule FinanceIndicatorDaemon.Statistics do
-  @data_service FinanceIndicatorDaemon.DataService
+  @data_service FinanceIndicatorDaemon.DatabaseService

   def get_crypto_statistics() do
     [:usd, :btc]
diff --git a/lib/finance_indicator_daemon.ex b/lib/finance_indicator_daemon.ex
index ff51ceb..faa0a43 100644
--- a/lib/finance_indicator_daemon.ex
+++ b/lib/finance_indicator_daemon.ex
@@ -8,6 +8,7 @@ defmodule FinanceIndicatorDaemon do
     Application.ensure_all_started(:inets)

     base_children = [
+      FinanceIndicatorDaemon.Repo,
       {Phoenix.PubSub, name: FinanceIndicatorDaemon.PubSub},
       {FinanceIndicatorDaemon.Endpoint, []}
     ]
diff --git a/lib/monitoring_controller.ex b/lib/monitoring_controller.ex
index a2619f5..e2154ee 100644
--- a/lib/monitoring_controller.ex
+++ b/lib/monitoring_controller.ex
@@ -3,7 +3,7 @@ defmodule FinanceIndicatorDaemon.MonitoringController do
   use OpenApiSpex.ControllerSpecs

   alias Api.IndicatorApi
-  @data_service FinanceIndicatorDaemon.DataService
+  @data_service FinanceIndicatorDaemon.DatabaseService

   operation :get_monitoring_statistics,
     responses: [
diff --git a/lib/tasks/data_mining.ex b/lib/tasks/data_mining.ex
index dce0c90..e1de71e 100644
--- a/lib/tasks/data_mining.ex
+++ b/lib/tasks/data_mining.ex
@@ -2,7 +2,7 @@ defmodule Tasks.DataMining do
   require Logger

   @cmc_client Coinmarketcap.Api.CoinmarketcapClient
-  @data_service FinanceIndicatorDaemon.DataService
+  @data_service FinanceIndicatorDaemon.DatabaseService

   def get_cryptocurrencies_mappings() do
     Logger.info("🗺️  Fetching cryptocurrencies mappings from CoinMarketCap")
@@ -53,37 +53,51 @@ defmodule Tasks.DataMining do

   defp get_coin_data_for_all_time(coin_id, currency) do
     Logger.info("📈 Fetching #{String.upcase(to_string(currency))} data for coin ID: #{coin_id}")
-    date_string = Timex.format!(DateTime.utc_now(), "{YYYY}{0M}{0D}")

-    if @data_service.does_coin_data_exist(coin_id, date_string, currency) do
-      Logger.info("✅ Data already exists for coin #{coin_id} (#{String.upcase(to_string(currency))}) on #{date_string}, skipping")
-    else
-      Logger.info("📊 Loading existing data for coin #{coin_id} (#{String.upcase(to_string(currency))})")
-      last_data = @data_service.find_latest_coin_data(coin_id, currency)
-      last_date_in_seconds = extract_last_timestamp(last_data)
+    current_datetime = DateTime.utc_now()
+    Logger.debug("🔍 Current UTC datetime: #{DateTime.to_iso8601(current_datetime)}")

-      Logger.info("🕐 Last data timestamp: #{last_date_in_seconds} (#{length(last_data)} existing records)")
+    date_string = Timex.format!(current_datetime, "{YYYY}{0M}{0D}")
+    Logger.info("🔍 Generated date string: '#{date_string}'")

-      Logger.info("🌐 Fetching new data from CoinMarketCap API for coin #{coin_id}")
-      start_time = System.monotonic_time(:millisecond)
+    try do
+      if @data_service.does_coin_data_exist(coin_id, date_string, currency) do
+        Logger.info("✅ Data already exists for coin #{coin_id} (#{String.upcase(to_string(currency))}) on #{date_string}, skipping")
+      else
+        Logger.info("📊 Loading existing data for coin #{coin_id} (#{String.upcase(to_string(currency))})")

-      case get_api_data(coin_id, last_date_in_seconds, currency) do
-        {:ok, body} ->
-          api_time = System.monotonic_time(:millisecond) - start_time
-          Logger.info("✅ API call completed in #{api_time}ms, received #{length(body)} new records")
+        last_data = @data_service.find_latest_coin_data(coin_id, currency)
+        Logger.debug("🔍 Found #{length(last_data)} existing records")

-          result = (last_data ++ body) |> Enum.uniq_by(&(&1["timestamp"]))
-          total_records = length(result)
+        last_date_in_seconds = extract_last_timestamp(last_data)
+        Logger.info("🕐 Last data timestamp: #{last_date_in_seconds} (#{length(last_data)} existing records)")

-          Logger.info("💾 Saving #{total_records} total records (#{length(last_data)} existing + #{length(body)} new)")
-          @data_service.write_coin_data(coin_id, date_string, result, currency)
+        Logger.info("🌐 Fetching new data from CoinMarketCap API for coin #{coin_id}")
+        start_time = System.monotonic_time(:millisecond)

-          Logger.info("✅ Successfully saved data for coin #{coin_id} (#{String.upcase(to_string(currency))})")
-        {:error, reason} ->
-          api_time = System.monotonic_time(:millisecond) - start_time
-          Logger.error("❌ API call failed after #{api_time}ms for coin #{coin_id}: #{inspect(reason)}")
-          {:error, reason}
+        case get_api_data(coin_id, last_date_in_seconds, currency) do
+          {:ok, body} ->
+            api_time = System.monotonic_time(:millisecond) - start_time
+            Logger.info("✅ API call completed in #{api_time}ms, received #{length(body)} new records")
+
+            result = (last_data ++ body) |> Enum.uniq_by(&(&1["timestamp"]))
+            total_records = length(result)
+
+            Logger.info("💾 Saving #{total_records} total records (#{length(last_data)} existing + #{length(body)} new)")
+            @data_service.write_coin_data(coin_id, date_string, result, currency)
+
+            Logger.info("✅ Successfully saved data for coin #{coin_id} (#{String.upcase(to_string(currency))})")
+          {:error, reason} ->
+            api_time = System.monotonic_time(:millisecond) - start_time
+            Logger.error("❌ API call failed after #{api_time}ms for coin #{coin_id}: #{inspect(reason)}")
+            {:error, reason}
+        end
       end
+    rescue
+      error ->
+        Logger.error("❌ Exception in get_coin_data_for_all_time: #{inspect(error)}")
+        Logger.error("❌ Stacktrace: #{Exception.format_stacktrace(__STACKTRACE__)}")
+        {:error, "Exception: #{inspect(error)}"}
     end
   end

@@ -95,6 +109,7 @@ defmodule Tasks.DataMining do
     |> Enum.take(-1)
     |> Enum.map(&(&1["timestamp"]))
     |> Enum.map(&convert_iso8601_to_system_time_seconds/1)
+    |> Enum.filter(&(&1 != :error))
     |> case do
       [] -> 0
       [timestamp] -> timestamp
diff --git a/lib/tasks/data_transformation.ex b/lib/tasks/data_transformation.ex
index 35ac028..1eaf3a3 100644
--- a/lib/tasks/data_transformation.ex
+++ b/lib/tasks/data_transformation.ex
@@ -1,7 +1,7 @@
 defmodule Tasks.DataTransformation do
   require Logger
   @indicator_api Api.IndicatorApi
-  @data_service FinanceIndicatorDaemon.DataService
+  @data_service FinanceIndicatorDaemon.DatabaseService
   alias FinanceIndicatorDaemon.RabbitMQ.Publisher

   def add_indicator_values do
@@ -24,26 +24,31 @@ defmodule Tasks.DataTransformation do
   defp process_currency_indicator_data(currency) do
     file_names = @data_service.read_all_coins_data_file_names(currency)
     total_files = length(file_names)
-    Logger.info("📁 Found #{total_files} #{String.upcase(to_string(currency))} data files to process")
+    Logger.info("📁 Found #{total_files} #{String.upcase(to_string(currency))} symbols to process")

     file_names
     |> Enum.with_index(1)
     |> Enum.each(fn {file_name, index} ->
-      Logger.info("🔄 Processing file #{index}/#{total_files}: #{file_name}")
+      Logger.info("🔄 Processing symbol #{index}/#{total_files}: #{file_name}")
       start_time = System.monotonic_time(:millisecond)

+      # Load ALL historical data for this symbol (chronologically ordered)
       data = @data_service.read_coin_data(file_name, currency)
       data_points = length(data)
-      Logger.info("📊 Loaded #{data_points} data points from #{file_name}")
-
-      case @indicator_api.get_data_with_indicator_data(data) do
-        {:ok, body} ->
-          @data_service.write_coin_indicator_data(body, file_name, currency)
-          processing_time = System.monotonic_time(:millisecond) - start_time
-          Logger.info("✅ Processed #{file_name} in #{processing_time}ms")
-        {:error, reason} ->
-          processing_time = System.monotonic_time(:millisecond) - start_time
-          Logger.error("❌ Failed to process #{file_name} after #{processing_time}ms: #{inspect(reason)}")
+      Logger.info("📊 Loaded #{data_points} historical data points for #{file_name}")
+
+      if data_points > 0 do
+        case @indicator_api.get_data_with_indicator_data(data) do
+          {:ok, body} ->
+            @data_service.write_coin_indicator_data(body, file_name, currency)
+            processing_time = System.monotonic_time(:millisecond) - start_time
+            Logger.info("✅ Processed #{file_name} in #{processing_time}ms")
+          {:error, reason} ->
+            processing_time = System.monotonic_time(:millisecond) - start_time
+            Logger.error("❌ Failed to process #{file_name} after #{processing_time}ms: #{inspect(reason)}")
+        end
+      else
+        Logger.warn("⚠️  No data found for #{file_name}, skipping")
       end
     end)
   end
@@ -98,10 +103,13 @@ defmodule Tasks.DataTransformation do
   end

   defp build_indicator_metadata(file, mappings) do
-    mapping = Enum.find(mappings["data"], &(&1["id"] == String.to_integer(Enum.at(file, 1))))
+    coin_id = String.to_integer(Enum.at(file, 1))
+    mapping = Enum.find(mappings["data"], &(&1["id"] == coin_id))
+    # Use today's date for statistics since we're now processing all historical data at once
+    today = Date.utc_today() |> Date.to_iso8601()
     %{
-      coin_id: Enum.at(file, 1),
-      date_string: String.replace(Enum.at(file, 2), ".json", ""),
+      coin_id: coin_id,
+      date_string: today,
       symbol: mapping["symbol"],
       slug: mapping["slug"],
       rank: mapping["rank"],
diff --git a/mix.exs b/mix.exs
index 7126283..3a5690f 100644
--- a/mix.exs
+++ b/mix.exs
@@ -30,7 +30,9 @@ defmodule FinanceIndicatorDaemon.MixProject do
       {:cowboy, "~> 2.8"},
       {:plug_cowboy, "~> 2.7.3"},
       {:amqp, "~> 4.0"},
-      {:open_api_spex, "~> 3.21"}
+      {:open_api_spex, "~> 3.21"},
+      {:ecto_sql, "~> 3.10"},
+      {:postgrex, "~> 0.17"}
     ]
   end
 end
diff --git a/test/docker-compose.test.yml b/test/docker-compose.test.yml
index ef0d3f5..d06e924 100644
--- a/test/docker-compose.test.yml
+++ b/test/docker-compose.test.yml
@@ -1,4 +1,35 @@
 services:
+  postgres:
+    image: postgres:15
+    environment:
+      POSTGRES_DB: financial_indicator_db
+      POSTGRES_USER: financial_user
+      POSTGRES_PASSWORD: financial_pass
+    networks:
+      - docker-network
+    healthcheck:
+      test: ["CMD-SHELL", "pg_isready -U financial_user -d financial_indicator_db"]
+      interval: 5s
+      timeout: 5s
+      retries: 5
+
+  liquibase:
+    image: liquibase/liquibase:4.25
+    depends_on:
+      postgres:
+        condition: service_healthy
+    environment:
+      LIQUIBASE_COMMAND_URL: ******************************************************
+      LIQUIBASE_COMMAND_USERNAME: financial_user
+      LIQUIBASE_COMMAND_PASSWORD: financial_pass
+      LIQUIBASE_COMMAND_CHANGELOG_FILE: changelog/db.changelog-master.sql
+    volumes:
+      - ../db/changelog:/liquibase/changelog
+      - ../db/liquibase.properties:/liquibase/liquibase.properties
+    networks:
+      - docker-network
+    command: ["liquibase", "update"]
+
   test:
     build:
       context: ..
@@ -8,7 +39,9 @@ services:
         - BUILD_DATE=${BUILD_DATE:-$(date -u +'%Y-%m-%dT%H:%M:%SZ')}
         - VCS_REF=${VCS_REF:-$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')}
         - CODE_HASH=${CODE_HASH:-$(find ../lib ../test ../config -type f -name "*.ex*" -exec md5sum {} \; 2>/dev/null | md5sum | cut -d' ' -f1 || echo 'unknown')}
-      # Remove no_cache to allow dependency layer caching
+    depends_on:
+      postgres:
+        condition: service_healthy
     env_file:
       - ../.env
     environment:
@@ -16,13 +49,13 @@ services:
       BASE_APP_PATH: /tmp/test
       CMC_API_THROTTLE_MIN: 300
       CMC_API_THROTTLE_MAX: 500
+      POSTGRES_HOST: postgres
     networks:
       - docker-network
     volumes:
       - ".:/app/test:ro"
       - "test_output:/tmp/test"
-      - "../out:/app/out"
-    command: ["mix", "test"]
+    command: ["mix", "test", "--only", "database"]

 volumes:
   test_output:
diff --git a/test/service/data_service_test.exs b/test/service/data_service_test.exs
index 92d9b7d..13999be 100644
--- a/test/service/data_service_test.exs
+++ b/test/service/data_service_test.exs
@@ -1,8 +1,8 @@
 defmodule FinanceIndicatorDaemon.DataServiceTest do
   use ExUnit.Case
-  doctest FinanceIndicatorDaemon.DataService
+  doctest FinanceIndicatorDaemon.DatabaseService

-  @data_service FinanceIndicatorDaemon.DataService
+  @data_service FinanceIndicatorDaemon.DatabaseService

   test "symbol override configuration resolves BOZO to correct coin ID" do
     # Test that BOZO symbol resolves to the configured override ID (29308) instead of default (28991)
diff --git a/test/tasks/data_mining_test.exs b/test/tasks/data_mining_test.exs
index b3180fd..1bb90ab 100644
--- a/test/tasks/data_mining_test.exs
+++ b/test/tasks/data_mining_test.exs
@@ -2,8 +2,9 @@ defmodule Tasks.DataMiningTest do
   use ExUnit.Case
   doctest Tasks.DataMining

-  @data_service FinanceIndicatorDaemon.DataService
+  @data_service FinanceIndicatorDaemon.DatabaseService

+  @tag :database
   test "get cryptocurrencies mappings" do
     @data_service.delete_mappings()
     assert !@data_service.do_mappings_exist()
