FROM python:3.11-slim

# Install cron
RUN apt-get update && apt-get install -y cron && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY cron/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the existing RabbitMQ scripts
COPY rabbit_mq/scripts/ ./scripts/
RUN chmod +x ./scripts/*.py

# Update scripts to use environment variables for RabbitMQ host
RUN sed -i "s/rabbitmq_host = 'localhost'/rabbitmq_host = os.getenv('RABBITMQ_HOST', 'localhost')/" ./scripts/*.py && \
    sed -i "1i import os" ./scripts/*.py

# Copy cron configuration
COPY cron/crontab /etc/cron.d/pipeline-cron
RUN chmod 0644 /etc/cron.d/pipeline-cron && \
    crontab /etc/cron.d/pipeline-cron

# Set environment variables with defaults
ENV RABBITMQ_HOST=finance_indicator_daemon_rabbitmq
ENV RABBITMQ_PORT=5672

# Create entrypoint script to ensure environment variables are available to cron
RUN echo '#!/bin/bash' > /entrypoint.sh && \
    echo 'printenv | grep -E "^RABBITMQ_" >> /etc/environment' >> /entrypoint.sh && \
    echo 'exec "$@"' >> /entrypoint.sh && \
    chmod +x /entrypoint.sh

ENTRYPOINT ["/entrypoint.sh"]
CMD ["cron", "-f"]
