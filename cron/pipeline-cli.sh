#!/bin/bash
set -e

# Financial Data Pipeline CLI Tool
# Interactive CLI for running individual pipeline scripts in Docker

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

CONTAINER_NAME="financial_pipeline_cron"

# Function to print colored output
print_header() {
    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}  Financial Data Pipeline CLI${NC}"
    echo -e "${CYAN}================================${NC}"
    echo ""
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# Function to check if Docker containers are running
check_docker_status() {
    print_info "Checking Docker container status..."
    
    if ! docker ps --format "table {{.Names}}" | grep -q "financial_pipeline_cron"; then
        print_error "Pipeline cron container is not running!"
        echo ""
        print_info "To start the pipeline container, run:"
        echo "  docker-compose --profile cron up -d pipeline-cron"
        echo ""
        exit 1
    fi
    
    if ! docker ps --format "table {{.Names}}" | grep -q "finance_indicator_daemon_rabbitmq"; then
        print_error "RabbitMQ container is not running!"
        echo ""
        print_info "To start RabbitMQ, run:"
        echo "  docker-compose up -d finance_indicator_daemon_rabbitmq"
        echo ""
        exit 1
    fi
    
    print_success "All required containers are running"
    echo ""
}

# Function to get user input for symbols
get_symbols_input() {
    echo -e "${PURPLE}Enter cryptocurrency symbols (space-separated):${NC}" >&2
    echo -e "${YELLOW}Examples: BTC ETH, BTC ETH ADA SOL, DOGE SHIB${NC}" >&2
    echo -n "Symbols: " >&2
    read -r symbols_input

    if [ -z "$symbols_input" ]; then
        echo -e "${RED}Error: No symbols provided. Please enter at least one symbol.${NC}" >&2
        return 1
    else
        echo "$symbols_input"
    fi
}

# Function to execute script in Docker container
execute_script() {
    local script_name="$1"
    local symbols="$2"
    
    print_info "Executing: $script_name"
    
    if [ -n "$symbols" ]; then
        print_info "Using symbols: $symbols"
        docker-compose --profile cron exec pipeline-cron python /app/scripts/"$script_name" --symbols $symbols
    else
        docker-compose --profile cron exec pipeline-cron python /app/scripts/"$script_name"
    fi
    
    if [ $? -eq 0 ]; then
        print_success "Script completed successfully"
    else
        print_error "Script failed"
        return 1
    fi
    echo ""
}

# Function to show script menu
show_menu() {
    echo -e "${PURPLE}Available Pipeline Scripts:${NC}"
    echo ""
    echo "1) Create Mappings (no parameters)"
    echo "2) Mine USD Data by Symbols (requires symbols)"
    echo "3) Mine BTC Data by Symbols (requires symbols)"
    echo "4) Create Indicator Data (no parameters)"
    echo "5) Create Statistics (no parameters)"
    echo "6) Run Full Pipeline (requires symbols)"
    echo "7) View Container Logs"
    echo "8) Check RabbitMQ Status"
    echo "0) Exit"
    echo ""
}

# Function to run full pipeline
run_full_pipeline() {
    local symbols="$1"
    
    print_info "Running full pipeline with symbols: $symbols"
    echo ""
    
    execute_script "create_mappings.py" ""
    sleep 2
    
    execute_script "mine_usd_data_by_symbols.py" "$symbols"
    sleep 2
    
    execute_script "mine_btc_data_by_symbols.py" "$symbols"
    sleep 2
    
    execute_script "create_indicator_data.py" ""
    sleep 2
    
    execute_script "create_statistics.py" ""
    
    print_success "Full pipeline completed!"
}

# Function to view logs
view_logs() {
    print_info "Showing pipeline container logs (press Ctrl+C to exit)..."
    echo ""
    docker-compose logs -f pipeline-cron
}

# Function to check RabbitMQ status
check_rabbitmq_status() {
    print_info "Checking RabbitMQ status..."
    echo ""
    
    # Check RabbitMQ management API
    if curl -s -u guest:guest http://localhost:15672/api/overview > /dev/null 2>&1; then
        print_success "RabbitMQ Management API is accessible"
        
        # Get queue information
        print_info "Queue status:"
        curl -s -u guest:guest http://localhost:15672/api/queues | \
        python3 -c "
import sys, json
data = json.load(sys.stdin)
for queue in data:
    name = queue['name']
    messages = queue.get('messages', 0)
    consumers = queue.get('consumers', 0)
    print(f'  {name}: {messages} messages, {consumers} consumers')
"
    else
        print_error "RabbitMQ Management API is not accessible"
        print_info "Make sure RabbitMQ container is running and port 15672 is accessible"
    fi
    echo ""
}

# Main function
main() {
    print_header
    check_docker_status
    
    while true; do
        show_menu
        echo -n "Select an option (0-9): "
        read -r choice
        echo ""
        
        case $choice in
            1)
                execute_script "create_mappings.py" ""
                ;;
            2)
                symbols=$(get_symbols_input)
                if [ $? -eq 0 ]; then
                    execute_script "mine_usd_data_by_symbols.py" "$symbols"
                fi
                ;;
            3)
                symbols=$(get_symbols_input)
                if [ $? -eq 0 ]; then
                    execute_script "mine_btc_data_by_symbols.py" "$symbols"
                fi
                ;;
            4)
                execute_script "create_indicator_data.py" ""
                ;;
            5)
                execute_script "create_statistics.py" ""
                ;;
            6)
                symbols=$(get_symbols_input)
                if [ $? -eq 0 ]; then
                    run_full_pipeline "$symbols"
                fi
                ;;
            7)
                view_logs
                ;;
            8)
                check_rabbitmq_status
                ;;
            0)
                print_info "Goodbye!"
                exit 0
                ;;
            *)
                print_error "Invalid option. Please select 0-8."
                echo ""
                ;;
        esac
        
        # Pause before showing menu again
        echo -e "${YELLOW}Press Enter to continue...${NC}"
        read -r
        echo ""
    done
}

# Run main function
main "$@"
