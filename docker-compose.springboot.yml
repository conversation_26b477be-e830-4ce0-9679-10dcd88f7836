services:
  postgres:
    image: postgres:15-alpine
    container_name: financial_postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-financial_indicator_db}
      POSTGRES_USER: ${POSTGRES_USER:-financial_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-financial_pass}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - docker-network
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-financial_user} -d ${POSTGRES_DB:-financial_indicator_db}" ]
      interval: 10s
      timeout: 5s
      retries: 5

  liquibase:
    image: liquibase/liquibase:4.25
    container_name: financial_liquibase
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./src/main/resources/db/changelog:/liquibase/changelog
    networks:
      - docker-network
    environment:
      LIQUIBASE_COMMAND_URL: *******************************/${POSTGRES_DB:-financial_indicator_db}
      LIQUIBASE_COMMAND_USERNAME: ${POSTGRES_USER:-financial_user}
      LIQUIBASE_COMMAND_PASSWORD: ${POSTGRES_PASSWORD:-financial_pass}
      LIQUIBASE_COMMAND_CHANGELOG_FILE: changelog/db.changelog-master.sql
      LIQUIBASE_COMMAND_DEFAULT_SCHEMA_NAME: financial_data
    command: [ "liquibase", "update" ]

  rabbitmq:
    image: rabbitmq:3-management
    container_name: financial_rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER:-guest}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD:-guest}
    volumes:
      - ./rabbit_mq/conf/definitions.json:/etc/rabbitmq/definitions.json:ro
      - ./rabbit_mq/conf/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf:ro
    ports:
      - "5672:5672"
      - "15672:15672"
    networks:
      - docker-network
    healthcheck:
      test: [ "CMD", "rabbitmqctl", "status" ]
      interval: 30s
      timeout: 10s
      retries: 5

  financial-indicator-daemon:
    build:
      context: .
      dockerfile: Dockerfile.springboot
    container_name: financial_indicator_daemon_springboot
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      liquibase:
        condition: service_completed_successfully
    networks:
      - docker-network
    environment:
      # Database Configuration
      - POSTGRES_DB=${POSTGRES_DB:-financial_indicator_db}
      - POSTGRES_USER=${POSTGRES_USER:-financial_user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-financial_pass}
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432

      # RabbitMQ Configuration
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USER=${RABBITMQ_USER:-guest}
      - RABBITMQ_PASSWORD=${RABBITMQ_PASSWORD:-guest}

      # External API Configuration
      - CMC_API_KEY=${CMC_API_KEY:-f8e302bb-197c-4a01-8fcf-136e9d334680}
      - CMC_API_THROTTLE_MIN=${CMC_API_THROTTLE_MIN:-300}
      - CMC_API_THROTTLE_MAX=${CMC_API_THROTTLE_MAX:-500}
      - CMC_SYMBOL_OVERRIDES=${CMC_SYMBOL_OVERRIDES:-}
      - INDICATOR_API_HOST=${INDICATOR_API_HOST:-http://localhost:5000}
      - AMOUNT_OF_COINS_TO_GET_BY_RANKING=${AMOUNT_OF_COINS_TO_GET_BY_RANKING:-3}

      # Spring Profile
      - SPRING_PROFILES_ACTIVE=docker
    ports:
      - "6601:6601"
    healthcheck:
      test: [ "CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:6601/actuator/health" ]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

networks:
  docker-network:
    driver: bridge

volumes:
  postgres_data:
