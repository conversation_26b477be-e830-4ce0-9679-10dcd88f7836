services:
  postgres:
    image: postgres:15-alpine
    container_name: financial_postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./db/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    networks:
      - docker-network
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}" ]
      interval: 10s
      timeout: 5s
      retries: 5

  finance_indicator_daemon:
    depends_on:
      postgres:
        condition: service_healthy
      finance_indicator_daemon_rabbitmq:
        condition: service_healthy
    build: .
    networks:
      - docker-network
    environment:
      - MIX_ENV=prod
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      - CMC_API_THROTTLE_MIN=${CMC_API_THROTTLE_MIN}
      - CMC_API_THROTTLE_MAX=${CMC_API_THROTTLE_MAX}
    volumes:
      - ./out:/app/out
    ports:
      - "6601:6601"

  finance_indicator_daemon_rabbitmq:
    image: rabbitmq:3-management
    healthcheck:
      test: [ "CMD", "rabbitmqctl", "status" ]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - docker-network
    volumes:
      - ./rabbit_mq/conf/definitions.json:/etc/rabbitmq/definitions.json:ro
      - ./rabbit_mq/conf/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf:ro
    ports:
      - "5672:5672"
      - "15672:15672"

  # Financial Data Pipeline Cron Scheduler
  pipeline-cron:
    build:
      context: .
      dockerfile: cron/Dockerfile
    container_name: financial_pipeline_cron
    depends_on:
      finance_indicator_daemon_rabbitmq:
        condition: service_healthy
    networks:
      - docker-network
    environment:
      # RabbitMQ Configuration
      - RABBITMQ_HOST=${RABBITMQ_HOST}
      - RABBITMQ_PORT=${RABBITMQ_PORT}
    restart: unless-stopped
    profiles:
      - cron

networks:
  docker-network:
    external: true

volumes:
  postgres_data:
