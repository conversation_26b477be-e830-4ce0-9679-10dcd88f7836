defmodule Api.IndicatorApi do
  require Logger

  def check_health() do
    url = "#{AppConfig.get_indicator_api_host()}/monitoring/health"
    Logger.info("🏥 Checking Indicator API health: #{url}")
    start_time = System.monotonic_time(:millisecond)

    case HTTPoison.get(url) do
      {:ok, %HTTPoison.Response{status_code: 200}} ->
        response_time = System.monotonic_time(:millisecond) - start_time
        Logger.info("✅ Indicator API is healthy (#{response_time}ms)")
        {:ok, "Indicator API is healthy"}
      {:ok, %HTTPoison.Response{status_code: status_code}} ->
        response_time = System.monotonic_time(:millisecond) - start_time
        Logger.error("❌ Indicator API unhealthy - status #{status_code} (#{response_time}ms)")
        {:error, "Indicator API returned non-200 status code: #{status_code}"}
      {:error, %HTTPoison.Error{reason: reason}} ->
        response_time = System.monotonic_time(:millisecond) - start_time
        Logger.error("❌ Indicator API request failed after #{response_time}ms: #{inspect(reason)}")
        {:error, "Indicator API request failed: #{inspect(reason)}"}
    end
  end

  def get_data_with_indicator_data(data) do
    send_post_request("#{AppConfig.get_indicator_api_host()}/larsson/calculate", data)
  end

  defp send_post_request(url, data) do
    json_data = if is_binary(data), do: data, else: Jason.encode!(data)
    data_size = byte_size(json_data)
    Logger.info("📤 Sending POST request to: #{url} (payload: #{data_size} bytes)")

    headers = [{"Content-Type", "application/json"}]
    start_time = System.monotonic_time(:millisecond)

    case HTTPoison.post(url, json_data, headers) do
      {:ok, %HTTPoison.Response{status_code: 200, body: body}} ->
        response_time = System.monotonic_time(:millisecond) - start_time
        response_size = byte_size(body)
        Logger.info("✅ POST request successful in #{response_time}ms (response: #{response_size} bytes)")
        {:ok, body}
      {:ok, %HTTPoison.Response{status_code: status_code, body: body}} ->
        response_time = System.monotonic_time(:millisecond) - start_time
        Logger.error("❌ POST request failed with status #{status_code} after #{response_time}ms: #{body}")
        {:error, body}
      {:error, %HTTPoison.Error{reason: reason}} ->
        response_time = System.monotonic_time(:millisecond) - start_time
        Logger.error("❌ POST request failed after #{response_time}ms: #{inspect(reason)}")
        {:error, reason}
    end
  end
end
