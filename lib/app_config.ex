defmodule AppConfig do
  def load_env, do: Dotenv.load()

  def get_variable(name, default \\ nil), do: System.get_env(name, default)

  defp ensure_path(path) do
    if path do
      case File.mkdir_p(path) do
        :ok ->
          require Logger
          Logger.debug("Created directory: #{path}")
        {:error, reason} ->
          require Logger
          Logger.error("Failed to create directory #{path}: #{inspect(reason)}")
      end
    end
    path
  end

  def get_base_app_path do
    get_variable("BASE_APP_PATH", "./out") |> ensure_path()
  end

  def get_mappings_out_path do
    get_variable("MAPPINGS_OUT_PATH", "./out/mappings") |> ensure_path()
  end

  def get_base_candle_data_out_path do
    get_variable("USD_BASE_CANDLE_DATA_OUT_PATH", "./out/candle_data/usd") |> ensure_path()
  end

  def get_btc_base_candle_data_out_path do
    get_variable("BTC_BASE_CANDLE_DATA_OUT_PATH", "./out/candle_data/btc") |> ensure_path()
  end

  def get_indicator_api_host do
    get_variable("INDICATOR_API_HOST", "http://localhost:6501")
  end

  def get_indicator_data_out_path do
    get_variable("USD_INDICATOR_DATA_OUT_PATH", "./out/indicator_data/usd") |> ensure_path()
  end

  def get_btc_indicator_data_out_path do
    get_variable("BTC_INDICATOR_DATA_OUT_PATH", "./out/indicator_data/btc") |> ensure_path()
  end

  def get_amount_of_coins_to_get_by_ranking do
    get_variable("AMOUNT_OF_COINS_TO_GET_BY_RANKING", "10") |> String.to_integer()
  end

  def get_statistics_out_path do
    get_variable("USD_STATISTICS_OUT_PATH", "./out/statistics/usd") |> ensure_path()
  end

  def get_btc_statistics_out_path do
    get_variable("BTC_STATISTICS_OUT_PATH", "./out/statistics/btc") |> ensure_path()
  end

  def get_rabbitmq_host, do: get_variable("RABBITMQ_HOST")

  def get_rabbitmq_port do
    get_variable("RABBITMQ_PORT") |> case do
      nil -> nil
      port_str -> String.to_integer(port_str)
    end
  end

  def rabbitmq_enabled?, do: get_rabbitmq_host() && get_rabbitmq_port()

  def get_cmc_api_throttle_min do
    get_variable("CMC_API_THROTTLE_MIN", "300") |> String.to_integer()
  end

  def get_cmc_api_throttle_max do
    get_variable("CMC_API_THROTTLE_MAX", "500") |> String.to_integer()
  end

  def get_cmc_symbol_overrides do
    case get_variable("CMC_SYMBOL_OVERRIDES") do
      nil -> %{}
      "" -> %{}
      overrides_str ->
        overrides_str
        |> String.split(",")
        |> Enum.map(&String.trim/1)
        |> Enum.filter(&(&1 != ""))
        |> Enum.map(&String.split(&1, ":"))
        |> Enum.filter(&(length(&1) == 2))
        |> Enum.map(fn [symbol, id_str] ->
          case Integer.parse(id_str) do
            {id, ""} -> {symbol, id}
            _ -> nil
          end
        end)
        |> Enum.filter(&(&1 != nil))
        |> Enum.into(%{})
    end
  end
end
