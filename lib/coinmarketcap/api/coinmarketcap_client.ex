defmodule Coinmarketcap.Api.CoinmarketcapClient do
  require <PERSON><PERSON>
  @json_utils Util.JsonUtils

  def get_cryptocurrencies_mappings() do
    Logger.info("🗺️  Requesting cryptocurrencies mappings from CoinMarketCap API")
    AppConfig.load_env()
    api_key = AppConfig.get_variable("CMC_API_KEY")
    api_key_preview = String.slice(api_key, 0, 8) <> "..."
    Logger.info("🔑 Using API key: #{api_key_preview}")

    # Apply throttling to avoid rate limiting
    sleep_time = get_throttle_delay()
    Logger.info("😴 API throttle delay: sleeping for #{sleep_time}ms before mappings API call")
    Process.sleep(sleep_time)

    headers = [{"X-CMC_PRO_API_KEY", api_key}, {"Accept", "application/json"}]
    url = "https://pro-api.coinmarketcap.com/v1/cryptocurrency/map"

    send_get_request(url, headers, [])
  end

  def get_data_in_btc(coin_id, end_date) do
    Logger.info("📊 Fetching BTC data for coin: #{coin_id}, end_date: #{end_date}")
    get_data([], coin_id, 1, :os.system_time(:seconds), end_date)
  end

  def get_data_in_usd(coin_id, end_date) do
    Logger.info("📊 Fetching USD data for coin: #{coin_id}, end_date: #{end_date}")
    get_data([], coin_id, 2781, :os.system_time(:seconds), end_date)
  end

  defp get_data(accumulator, coin_id, value_id, start_date, end_date) do
    if start_date < end_date do
      {:ok, convert_accumulator_to_data(accumulator)}
    else
      end_date_step = start_date - 15_552_000
      url = "https://api.coinmarketcap.com/data-api/v3.1/cryptocurrency/historical?id=#{coin_id}&timeStart=#{end_date_step}&timeEnd=#{start_date}&interval=1d&convertId=#{value_id}"
      user_agents = [
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"
      ]

      headers = [
        {"User-Agent", Enum.random(user_agents)},
        {"Accept", "*/*"},
        {"Accept-Language", "en-US,en;q=0.9,ru;q=0.8,et;q=0.7"},
        {"Origin", "https://coinmarketcap.com"},
        {"Referer", "https://coinmarketcap.com/"},
        {"Sec-Fetch-Dest", "empty"},
        {"Sec-Fetch-Mode", "cors"},
        {"Sec-Fetch-Site", "same-site"},
        {"Priority", "u=1, i"},
        {"Cache-Control", "no-cache"},
        {"Platform", "web"},
        {"X-Request-Id", generate_request_id()}
      ]

      sleep_time = get_throttle_delay()
      Logger.info("😴 API throttle delay: sleeping for #{sleep_time}ms before API call")
      Process.sleep(sleep_time)

      case send_get_request(url, headers, []) do
        {:ok, body} ->
          json = Jason.decode!(body)
          quotes = @json_utils.get_json_value(json, "data.quotes")
          quotes_count = length(quotes)

          Logger.info("📈 Received #{quotes_count} data points from API")

          if quotes_count < 1 do
            total_records = accumulator |> Enum.map(&length(@json_utils.get_json_value(&1, "data.quotes"))) |> Enum.sum()
            Logger.info("✅ API pagination complete, total records collected: #{total_records}")
            {:ok, convert_accumulator_to_data(accumulator)}
          else
            Logger.info("🔄 Continuing pagination, fetching next batch...")
            get_data(accumulator ++ [json], coin_id, value_id, end_date_step, end_date)
          end
        {:error, reason} ->
          Logger.error("❌ API request failed: #{inspect(reason)}")
          {:error, reason}
      end
    end
  end

  def convert_accumulator_to_data(accumulator) do
    accumulator
    |> Enum.map(&@json_utils.get_json_value(&1, "data"))
    |> Enum.map(&@json_utils.get_json_value(&1, "quotes"))
    |> Enum.flat_map(& &1)
    |> Enum.map(&@json_utils.get_json_value(&1, "quote"))
    |> Enum.sort(&compare_timestamps/2)
  end

  defp compare_timestamps(x, y) do
    {:ok, datetime_x, _} = DateTime.from_iso8601(@json_utils.get_json_value(x, "timestamp"))
    {:ok, datetime_y, _} = DateTime.from_iso8601(@json_utils.get_json_value(y, "timestamp"))
    DateTime.compare(datetime_x, datetime_y) == :lt
  end

  defp send_get_request(url, headers, params) do
    Logger.info("🌐 Sending GET request to: #{url}")
    start_time = System.monotonic_time(:millisecond)

    case HTTPoison.get(url, headers, query: params) do
      {:ok, %{status_code: 200, body: body}} ->
        request_time = System.monotonic_time(:millisecond) - start_time
        body_size = byte_size(body)
        Logger.info("✅ Request successful in #{request_time}ms, response size: #{body_size} bytes")
        {:ok, body}
      {:ok, %{status_code: status_code, body: body}} ->
        request_time = System.monotonic_time(:millisecond) - start_time
        Logger.error("❌ Request failed with status #{status_code} after #{request_time}ms: #{body}")
        {:error, "HTTP #{status_code}: #{body}"}
      {:error, reason} ->
        request_time = System.monotonic_time(:millisecond) - start_time
        Logger.error("❌ Request failed after #{request_time}ms: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp generate_request_id() do
    :crypto.strong_rand_bytes(16) |> Base.encode16(case: :lower)
  end

  defp get_throttle_delay() do
    min_delay = AppConfig.get_cmc_api_throttle_min()
    max_delay = AppConfig.get_cmc_api_throttle_max()
    :rand.uniform(max_delay - min_delay) + min_delay
  end
end
