defmodule FinanceIndicatorDaemon.Consumer do
  use GenServer
  use AMQP

  require Logger

  def start_link(opts) do
    GenServer.start_link(__MODULE__, opts, name: Keyword.get(opts, :name))
  end

  def init(opts) do
    queue = Keyword.fetch!(opts, :queue)
    Logger.info("Starting consumer for queue: #{queue}")
    process_function = Keyword.fetch!(opts, :process_function)

    host = AppConfig.get_rabbitmq_host()
    port = AppConfig.get_rabbitmq_port()

    {:ok, conn} = Connection.open("amqp://guest:guest@#{host}:#{port}")

    {:ok, chan} = Channel.open(conn)
    # Limit unacknowledged messages to 10
    :ok = Basic.qos(chan, prefetch_count: 10)
    # Register the GenServer process as a consumer
    {:ok, _consumer_tag} = Basic.consume(chan, queue)
    {:ok, %{channel: chan, process_function: process_function}}
  end

  def handle_info({:basic_consume_ok, %{consumer_tag: _consumer_tag}}, state) do
    Logger.info("Consumer started successfully")
    {:noreply, state}
  end

  def handle_info({:basic_cancel, %{consumer_tag: _consumer_tag}}, state) do
    {:stop, :normal, state}
  end

  def handle_info({:basic_cancel_ok, %{consumer_tag: _consumer_tag}}, state) do
    {:noreply, state}
  end

  def handle_info(
        {:basic_deliver, payload, %{delivery_tag: tag}},
        %{channel: chan, process_function: fun} = state
      ) do
    Logger.info("Received message with delivery_tag: #{tag}, payload size: #{byte_size(payload)} bytes")
    start_time = System.monotonic_time(:millisecond)

    try do
      fun.(chan, tag, payload)
      processing_time = System.monotonic_time(:millisecond) - start_time
      Logger.info("Message processed successfully in #{processing_time}ms (delivery_tag: #{tag})")
    rescue
      error ->
        processing_time = System.monotonic_time(:millisecond) - start_time
        Logger.error("Message processing failed after #{processing_time}ms (delivery_tag: #{tag}): #{inspect(error)}")
        AMQP.Basic.reject(chan, tag, requeue: false)
    end

    {:noreply, state}
  end

  def handle_info(message, state) do
    Logger.info(message, label: "Unexpected message")
    {:noreply, state}
  end
end
