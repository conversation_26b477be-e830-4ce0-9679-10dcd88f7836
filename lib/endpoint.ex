defmodule FinanceIndicatorDaemon.Endpoint do
  use Phoenix.Endpoint, otp_app: :finance_indicator_daemon

  socket("/socket", FinanceIndicatorDaemon.UserSocket)

  plug(Plug.RequestId)
  plug(Plug.Telemetry, event_prefix: [:phoenix, :endpoint])

  plug(Plug.Parsers,
    parsers: [:urlencoded, :multipart, :json],
    pass: ["*/*"],
    json_decoder: Phoenix.json_library()
  )

  plug(Plug.MethodOverride)
  plug(Plug.Head)
  plug(Plug.Session, store: :cookie, key: "_your_app_key", signing_salt: "secret")

  plug(FinanceIndicatorDaemon.Router)
end
