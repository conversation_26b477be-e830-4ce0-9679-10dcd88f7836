defmodule FinanceIndicatorDaemon do
  require Logger
  alias FinanceIndicatorDaemon.RabbitMQ.Config, as: RabbitMQConfig

  def start(_type, _args) do
    Application.ensure_all_started(:hackney)
    Application.ensure_all_started(:ssl)
    Application.ensure_all_started(:inets)

    base_children = [
      {Phoenix.PubSub, name: FinanceIndicatorDaemon.PubSub},
      {FinanceIndicatorDaemon.Endpoint, []}
    ]

    children = if AppConfig.rabbitmq_enabled?() do
      Logger.info("RabbitMQ enabled, starting consumers")
      base_children ++ RabbitMQConfig.consumer_specs()
    else
      Logger.info("RabbitMQ disabled")
      base_children
    end

    Supervisor.start_link(children, strategy: :one_for_one, name: FinanceIndicatorDaemon.Supervisor)
  end
end
