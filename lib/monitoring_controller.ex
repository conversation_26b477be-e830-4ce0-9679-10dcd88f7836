defmodule FinanceIndicatorDaemon.MonitoringController do
  use Phoenix.Controller
  use OpenApiSpex.ControllerSpecs

  alias Api.IndicatorApi
  @data_service FinanceIndicatorDaemon.DataService

  operation :get_monitoring_statistics,
    responses: [
      ok: {"Health status", "application/json", FinanceIndicatorDaemon.Schemas.HealthResponse}
    ]

  def get_monitoring_statistics(conn, _params) do
    {status, message} = IndicatorApi.check_health()

    response = %{
      success: status == :ok,
      services: %{indicator_api: message},
      files: @data_service.list_files() |> Enum.take(5)
    }

    json(conn, response)
  end
end
