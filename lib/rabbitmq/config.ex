defmodule FinanceIndicatorDaemon.RabbitMQ.Config do
  require Logger
  @data_mining Tasks.DataMining
  @data_transformation Tasks.DataTransformation
  alias FinanceIndicatorDaemon.RabbitMQ.Publisher

  def consumer_specs, do: consumer_configs() |> Enum.map(&build_consumer_spec/1)

  def consumer_configs do
    [
      {"mine_usd_data_by_symbols", :symbols_processor, :usd},
      {"mine_btc_data_by_symbols", :symbols_processor, :btc},
      {"create_indicator_data", :simple_processor, &@data_transformation.add_indicator_values/0},
      {"create_statistics", :simple_processor, &@data_transformation.create_statistics/0},
      {"create_mappings", :simple_processor, &@data_mining.get_cryptocurrencies_mappings/0}
    ]
  end

  def build_consumer_spec({queue, processor_type, processor_arg}) do
    process_function = build_process_function(processor_type, processor_arg)
    consumer_id = String.to_atom("consumer_#{queue}")
    Supervisor.child_spec(
      {FinanceIndicatorDaemon.Consumer, queue: queue, process_function: process_function},
      id: consumer_id
    )
  end

  def build_process_function(:symbols_processor, currency) do
    fn channel, tag, payload ->
      case Jason.decode(payload) do
        {:ok, %{"symbols" => symbols}} ->
          AMQP.Basic.ack(channel, tag)
          Logger.info("🔄 Starting #{String.upcase(to_string(currency))} data mining for #{length(symbols)} symbols: #{inspect(symbols)}")
          start_time = System.monotonic_time(:millisecond)

          result = process_symbols_for_currency(symbols, currency)

          processing_time = System.monotonic_time(:millisecond) - start_time
          Logger.info("✅ Completed #{String.upcase(to_string(currency))} data mining for #{length(symbols)} symbols in #{processing_time}ms")
          result
        {:error, decode_error} ->
          Logger.error("❌ Failed to decode JSON payload: #{inspect(decode_error)}, payload: #{payload}")
          AMQP.Basic.reject(channel, tag, requeue: false)
        _ ->
          Logger.error("❌ Invalid payload format - missing 'symbols' key, payload: #{payload}")
          AMQP.Basic.reject(channel, tag, requeue: false)
      end
    end
  end

  def build_process_function(:simple_processor, function) do
    fn channel, tag, _payload ->
      AMQP.Basic.ack(channel, tag)
      function_name = "#{inspect(function)}"
      Logger.info("🔄 Executing simple processor: #{function_name}")
      start_time = System.monotonic_time(:millisecond)

      result = function.()

      processing_time = System.monotonic_time(:millisecond) - start_time
      Logger.info("✅ Completed simple processor: #{function_name} in #{processing_time}ms")
      result
    end
  end

  def process_symbols_for_currency(symbols, currency) when currency in [:usd, :btc] do
    Logger.info("📊 Processing #{length(symbols)} symbols for #{String.upcase(to_string(currency))} currency")

    symbols
    |> Enum.with_index(1)
    |> Enum.each(fn {symbol, index} ->
      Logger.info("🪙 Processing symbol #{index}/#{length(symbols)}: #{symbol} (#{String.upcase(to_string(currency))})")
      start_time = System.monotonic_time(:millisecond)

      case currency do
        :usd -> @data_mining.get_coin_usd_data_for_all_time_by_symbol(symbol)
        :btc -> @data_mining.get_coin_btc_data_for_all_time_by_symbol(symbol)
      end

      processing_time = System.monotonic_time(:millisecond) - start_time
      Logger.info("✅ Completed #{symbol} (#{String.upcase(to_string(currency))}) in #{processing_time}ms")
    end)

    Logger.info("🚀 Publishing create_indicator_data message after processing all symbols")
    Publisher.publish_create_indicator_data()
  end
end
