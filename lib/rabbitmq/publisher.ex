defmodule FinanceIndicatorDaemon.RabbitMQ.Publisher do
  use AMQP
  require Lo<PERSON>

  def publish_create_indicator_data, do: publish_message("create_indicator_data", %{})
  def publish_create_statistics, do: publish_message("create_statistics", %{})

  def publish_message(queue_name, payload) when is_binary(queue_name) do
    message = Jason.encode!(payload)
    message_size = byte_size(message)
    Logger.info("📤 Publishing message to queue: #{queue_name} (#{message_size} bytes)")
    start_time = System.monotonic_time(:millisecond)

    with {:ok, connection} <- get_connection(),
         {:ok, channel} <- Channel.open(connection),
         :ok <- Basic.publish(channel, "", queue_name, message, persistent: true) do
      Channel.close(channel)
      Connection.close(connection)

      publish_time = System.monotonic_time(:millisecond) - start_time
      Logger.info("✅ Successfully published message to #{queue_name} in #{publish_time}ms")
      :ok
    else
      {:error, reason} ->
        publish_time = System.monotonic_time(:millisecond) - start_time
        Logger.error("❌ Failed to publish to #{queue_name} after #{publish_time}ms: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp get_connection do
    host = AppConfig.get_rabbitmq_host()
    port = AppConfig.get_rabbitmq_port()
    if host && port, do: Connection.open("amqp://guest:guest@#{host}:#{port}"), else: {:error, :rabbitmq_not_configured}
  end
end
