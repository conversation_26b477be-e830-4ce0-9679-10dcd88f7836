defmodule FinanceIndicatorDaemon.Router do
  use Phoenix.Router

  pipeline :api do
    plug(:accepts, ["json"])
    plug(OpenApiSpex.Plug.PutApiSpec, module: FinanceIndicatorDaemon.ApiSpec)
  end

  scope "/docs" do
    pipe_through(:api)
    get("/", OpenApiSpex.Plug.SwaggerUI, path: "/docs/openapi.json")
    get("/openapi.json", OpenApiSpex.Plug.RenderSpec, [])
  end

  scope "/api/v1" do
    pipe_through(:api)
    get("/crypto/statistics", FinanceIndicatorDaemon.StatisticsController, :get_crypto_statistics)
  end

  scope "/monitoring" do
    pipe_through(:api)
    get("/health", FinanceIndicatorDaemon.MonitoringController, :get_monitoring_statistics)
  end
end
