defmodule FinanceIndicatorDaemon.Schemas do
  alias OpenApiSpex.Schema

  defmodule FileInfo do
    require OpenApiSpex

    OpenApiSpex.schema(%{
      type: :object,
      properties: %{
        path: %Schema{type: :string},
        size: %Schema{type: :integer},
        last_modified: %Schema{type: :string, format: :"date-time"},
        type: %Schema{type: :string}
      }
    })
  end

  defmodule Services do
    require OpenApiSpex

    OpenApiSpex.schema(%{
      type: :object,
      properties: %{
        indicator_api: %Schema{type: :string}
      }
    })
  end

  defmodule HealthResponse do
    require OpenApiSpex

    OpenApiSpex.schema(%{
      type: :object,
      properties: %{
        success: %Schema{type: :boolean},
        services: Services,
        files: %Schema{type: :array, items: FileInfo}
      }
    })
  end

  defmodule IndicatorData do
    require OpenApiSpex

    OpenApiSpex.schema(%{
      type: :object,
      properties: %{
        close: %Schema{type: :number},
        color: %Schema{type: :string},
        high: %Schema{type: :number},
        hl2: %Schema{type: :number},
        low: %Schema{type: :number},
        marketCap: %Schema{type: :number},
        name: %Schema{type: :string},
        open: %Schema{type: :number},
        p1: %Schema{type: :boolean},
        p2: %Schema{type: :boolean},
        p3: %Schema{type: :boolean},
        smma_15: %Schema{type: :number},
        smma_19: %Schema{type: :number},
        smma_25: %Schema{type: :number},
        smma_29: %Schema{type: :number},
        timestamp: %Schema{type: :string, format: :"date-time"},
        volume: %Schema{type: :number}
      }
    })
  end

  defmodule CryptoStatistic do
    require OpenApiSpex

    OpenApiSpex.schema(%{
      type: :object,
      properties: %{
        date_string: %Schema{type: :string},
        latest_indicator_data: %Schema{type: :array, items: IndicatorData},
        rank: %Schema{type: :integer},
        slug: %Schema{type: :string},
        symbol: %Schema{type: :string}
      }
    })
  end

  defmodule CryptoStatisticsResponse do
    require OpenApiSpex

    OpenApiSpex.schema(%{
      type: :object,
      properties: %{
        usd: %Schema{type: :array, items: CryptoStatistic},
        btc: %Schema{type: :array, items: CryptoStatistic}
      }
    })
  end

  defmodule ErrorResponse do
    require OpenApiSpex

    OpenApiSpex.schema(%{
      type: :object,
      properties: %{
        error: %Schema{type: :string}
      }
    })
  end
end
