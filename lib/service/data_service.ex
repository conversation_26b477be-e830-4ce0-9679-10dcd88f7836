defmodule FinanceIndicatorDaemon.DataService do
  require Logger

  @mappings_file "cmc_mappings.json"

  defp build_file_path(base_path, prefix, coin_id, date_string) do
    Path.join(base_path, "#{prefix}_data_for_all_time-#{coin_id}-#{date_string}.json")
  end

  defp build_mappings_path, do: Path.join(AppConfig.get_mappings_out_path(), @mappings_file)

  def do_mappings_exist(), do: File.exists?(build_mappings_path())

  def write_mappings(mappings) do
    path = build_mappings_path()
    Logger.info("Writing data to file: #{path}")
    File.write!(path, mappings)
  end

  def delete_mappings() do
    path = build_mappings_path()
    Logger.info("Deleting file: #{path}")
    if do_mappings_exist(), do: File.rm!(path)
  end

  def read_mappings(), do: Jason.decode!(File.read!(build_mappings_path()))

  def get_coin_id(symbol) do
    Logger.info("Getting coin id by symbol: #{symbol}")

    symbol_overrides = AppConfig.get_cmc_symbol_overrides()
    case Map.get(symbol_overrides, symbol) do
      nil ->
        mappings = read_mappings()
        case Enum.find(mappings["data"], &(&1["symbol"] == symbol)) do
          nil ->
            Logger.error("No coin found for symbol: #{symbol}")
            nil
          coin ->
            Logger.info("Found coin ID #{coin["id"]} for symbol: #{symbol}")
            coin["id"]
        end
      override_id ->
        Logger.info("Using override: symbol #{symbol} -> coin ID #{override_id}")
        override_id
    end
  end

  def does_coin_data_exist(coin_id, date_string, :usd) do
    build_file_path(AppConfig.get_base_candle_data_out_path(), "usd", coin_id, date_string)
    |> File.exists?()
  end

  def does_coin_data_exist(coin_id, date_string, :btc) do
    build_file_path(AppConfig.get_btc_base_candle_data_out_path(), "btc", coin_id, date_string)
    |> File.exists?()
  end

  def write_coin_data(coin_id, date_string, data, :usd) do
    file_path = build_file_path(AppConfig.get_base_candle_data_out_path(), "usd", coin_id, date_string)
    write_json_file(file_path, data)
  end

  def write_coin_data(coin_id, date_string, data, :btc) do
    file_path = build_file_path(AppConfig.get_btc_base_candle_data_out_path(), "btc", coin_id, date_string)
    write_json_file(file_path, data)
  end

  defp write_json_file(file_path, data) do
    data_size = data |> Jason.encode!() |> byte_size()
    Logger.info("💾 Writing #{data_size} bytes to file: #{file_path}")

    # Ensure directory exists
    file_path |> Path.dirname() |> File.mkdir_p!()

    File.write!(file_path, Jason.encode!(data))
    Logger.info("✅ Successfully wrote file: #{file_path}")
  end

  def find_latest_coin_data(coin_id, :usd), do: find_latest_coin_data_by_path(coin_id, AppConfig.get_base_candle_data_out_path())
  def find_latest_coin_data(coin_id, :btc), do: find_latest_coin_data_by_path(coin_id, AppConfig.get_btc_base_candle_data_out_path())

  defp find_latest_coin_data_by_path(coin_id, path) do
    path
    |> File.ls!()
    |> Enum.filter(&String.ends_with?(&1, ".json"))
    |> Enum.map(&String.split(&1, "-"))
    |> Enum.filter(&(Enum.at(&1, 1) == "#{coin_id}"))
    |> Enum.sort(&(Enum.at(&1, 2) < Enum.at(&2, 2)))
    |> Enum.take(-1)
    |> Enum.map(&Enum.join(&1, "-"))
    |> Enum.map(&Path.join(path, &1))
    |> Enum.flat_map(&(File.read!(&1) |> Jason.decode!()))
    |> Enum.sort(&compare_timestamps/2)
  end

  defp compare_timestamps(x, y) do
    {:ok, datetime_x, _} = DateTime.from_iso8601(x["timestamp"])
    {:ok, datetime_y, _} = DateTime.from_iso8601(y["timestamp"])
    DateTime.compare(datetime_x, datetime_y) == :lt
  end

  def read_all_coins_data_file_names(:usd), do: read_all_file_names_in_dir(AppConfig.get_base_candle_data_out_path())
  def read_all_coins_data_file_names(:btc), do: read_all_file_names_in_dir(AppConfig.get_btc_base_candle_data_out_path())

  def read_coin_indicator_data(coin_id, date_string, :usd) do
    build_file_path(AppConfig.get_indicator_data_out_path(), "usd", coin_id, date_string)
    |> read_json_file()
  end

  def read_coin_indicator_data(coin_id, date_string, :btc) do
    build_file_path(AppConfig.get_btc_indicator_data_out_path(), "btc", coin_id, date_string)
    |> read_json_file()
  end

  def read_coin_data(file_name, :usd), do: read_file_from_path(AppConfig.get_base_candle_data_out_path(), file_name)
  def read_coin_data(file_name, :btc), do: read_file_from_path(AppConfig.get_btc_base_candle_data_out_path(), file_name)

  defp read_json_file(file_path) do
    Logger.info("Reading data from file: #{file_path}")
    Jason.decode!(File.read!(file_path))
  end

  defp read_file_from_path(base_path, file_name) do
    file_path = Path.join(base_path, file_name)
    Logger.info("Reading data from file: #{file_path}")
    Jason.decode!(File.read!(file_path))
  end

  def read_all_coins_indicator_data_file_names(:usd), do: read_all_file_names_in_dir(AppConfig.get_indicator_data_out_path())
  def read_all_coins_indicator_data_file_names(:btc), do: read_all_file_names_in_dir(AppConfig.get_btc_indicator_data_out_path())

  def write_coin_indicator_data(data, file_name, :usd) do
    write_cleaned_data_to_path(AppConfig.get_indicator_data_out_path(), file_name, data)
  end

  def write_coin_indicator_data(data, file_name, :btc) do
    write_cleaned_data_to_path(AppConfig.get_btc_indicator_data_out_path(), file_name, data)
  end

  def write_statistics(data, coin_id, date_string, :usd) do
    build_file_path(AppConfig.get_statistics_out_path(), "usd", coin_id, date_string)
    |> write_json_file(data)
  end

  def write_statistics(data, coin_id, date_string, :btc) do
    build_file_path(AppConfig.get_btc_statistics_out_path(), "btc", coin_id, date_string)
    |> write_json_file(data)
  end

  defp write_cleaned_data_to_path(base_path, file_name, data) do
    file_path = Path.join(base_path, file_name)

    # Ensure directory exists
    File.mkdir_p!(base_path)

    cleaned_data = String.replace(data, "NaN", "null")
    data_size = byte_size(cleaned_data)
    Logger.info("💾 Writing #{data_size} bytes (cleaned) to file: #{file_path}")

    File.write!(file_path, cleaned_data)
    Logger.info("✅ Successfully wrote cleaned data file: #{file_path}")
  end

  def read_all_latest_statistics(:usd) do
    path = AppConfig.get_statistics_out_path()
    Logger.info("Reading USD data from path: #{path}")
    read_latest_statistics_from_path(path)
  end

  def read_all_latest_statistics(:btc) do
    path = AppConfig.get_btc_statistics_out_path()
    Logger.info("Reading BTC data from path: #{path}")
    read_latest_statistics_from_path(path)
  end

  defp read_latest_statistics_from_path(path) do
    File.ls!(path)
    |> Enum.filter(&String.ends_with?(&1, ".json"))
    |> Enum.map(&String.split(&1, "-"))
    |> Enum.group_by(&Enum.at(&1, 1))
    |> Enum.map(fn {_, files} -> Enum.sort(files, &(Enum.at(&1, 2) > Enum.at(&2, 2))) |> hd() end)
    |> Enum.map(&(Path.join(path, Enum.join(&1, "-")) |> File.read!() |> Jason.decode!()))
  end

  def list_files(), do: list_files_recursive(AppConfig.get_base_app_path(), [])

  defp list_files_recursive(path, acc) do
    case File.ls(path) do
      {:ok, entries} ->
        Enum.reduce(entries, acc, fn entry, acc ->
          full_path = Path.join(path, entry)
          if File.dir?(full_path), do: list_files_recursive(full_path, acc), else: [full_path | acc]
        end)
      {:error, _} -> acc
    end
  end

  defp read_all_file_names_in_dir(dir_path) do
    Logger.info("Reading data from path: #{dir_path}")
    File.ls!(dir_path) |> Enum.filter(&String.ends_with?(&1, ".json"))
  end
end
