defmodule FinanceIndicatorDaemon.StatisticsController do
  use Phoenix.Controller
  use OpenApiSpex.ControllerSpecs

  @statistics_execution FinanceIndicatorDaemon.Statistics

  operation :get_crypto_statistics,
    responses: [
      ok: {"Statistics", "application/json", FinanceIndicatorDaemon.Schemas.CryptoStatisticsResponse}
    ]

  def get_crypto_statistics(conn, _params) do
    json(conn, @statistics_execution.get_crypto_statistics())
  end
end
