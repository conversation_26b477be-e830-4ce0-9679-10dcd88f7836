defmodule Tasks.DataMining do
  require Logger

  @cmc_client Coinmarketcap.Api.CoinmarketcapClient
  @data_service FinanceIndicatorDaemon.DataService

  def get_cryptocurrencies_mappings() do
    Logger.info("🗺️  Fetching cryptocurrencies mappings from CoinMarketCap")

    if @data_service.do_mappings_exist() do
      Logger.info("✅ Mappings already exist, skipping download")
    else
      Logger.info("📥 Mappings not found, downloading from CoinMarketCap API")
      start_time = System.monotonic_time(:millisecond)

      case @cmc_client.get_cryptocurrencies_mappings() do
        {:ok, body} ->
          @data_service.write_mappings(body)
          processing_time = System.monotonic_time(:millisecond) - start_time
          Logger.info("✅ Successfully downloaded and saved mappings in #{processing_time}ms")
        {:error, reason} ->
          processing_time = System.monotonic_time(:millisecond) - start_time
          Logger.error("❌ Failed to download mappings after #{processing_time}ms: #{inspect(reason)}")
          raise "Failed to download mappings: #{inspect(reason)}"
      end
    end
  end

  defp convert_iso8601_to_system_time_seconds(iso_timestamp) do
    case DateTime.from_iso8601(iso_timestamp) do
      {:ok, datetime, _} -> DateTime.to_unix(datetime, :second)
      {:error, _} -> :error
    end
  end

  def get_coin_usd_data_for_all_time_by_symbol(symbol), do: get_coin_data_by_symbol(symbol, :usd)
  def get_coin_btc_data_for_all_time_by_symbol(symbol), do: get_coin_data_by_symbol(symbol, :btc)
  def get_coin_usd_data_for_all_time(coin_id), do: get_coin_data_for_all_time(coin_id, :usd)
  def get_coin_btc_data_for_all_time(coin_id), do: get_coin_data_for_all_time(coin_id, :btc)

  defp get_coin_data_by_symbol(symbol, currency) do
    Logger.info("🔍 Looking up coin ID for symbol: #{symbol}")

    case @data_service.get_coin_id(symbol) do
      nil ->
        Logger.error("❌ Could not find coin ID for symbol: #{symbol}")
        {:error, "Symbol not found: #{symbol}"}
      coin_id ->
        Logger.info("✅ Found coin ID #{coin_id} for symbol: #{symbol}")
        get_coin_data_for_all_time(coin_id, currency)
    end
  end

  defp get_coin_data_for_all_time(coin_id, currency) do
    Logger.info("📈 Fetching #{String.upcase(to_string(currency))} data for coin ID: #{coin_id}")
    date_string = Timex.format!(DateTime.utc_now(), "{YYYY}{0M}{0D}")

    if @data_service.does_coin_data_exist(coin_id, date_string, currency) do
      Logger.info("✅ Data already exists for coin #{coin_id} (#{String.upcase(to_string(currency))}) on #{date_string}, skipping")
    else
      Logger.info("📊 Loading existing data for coin #{coin_id} (#{String.upcase(to_string(currency))})")
      last_data = @data_service.find_latest_coin_data(coin_id, currency)
      last_date_in_seconds = extract_last_timestamp(last_data)

      Logger.info("🕐 Last data timestamp: #{last_date_in_seconds} (#{length(last_data)} existing records)")

      Logger.info("🌐 Fetching new data from CoinMarketCap API for coin #{coin_id}")
      start_time = System.monotonic_time(:millisecond)

      case get_api_data(coin_id, last_date_in_seconds, currency) do
        {:ok, body} ->
          api_time = System.monotonic_time(:millisecond) - start_time
          Logger.info("✅ API call completed in #{api_time}ms, received #{length(body)} new records")

          result = (last_data ++ body) |> Enum.uniq_by(&(&1["timestamp"]))
          total_records = length(result)

          Logger.info("💾 Saving #{total_records} total records (#{length(last_data)} existing + #{length(body)} new)")
          @data_service.write_coin_data(coin_id, date_string, result, currency)

          Logger.info("✅ Successfully saved data for coin #{coin_id} (#{String.upcase(to_string(currency))})")
        {:error, reason} ->
          api_time = System.monotonic_time(:millisecond) - start_time
          Logger.error("❌ API call failed after #{api_time}ms for coin #{coin_id}: #{inspect(reason)}")
          {:error, reason}
      end
    end
  end

  defp get_api_data(coin_id, timestamp, :usd), do: @cmc_client.get_data_in_usd(coin_id, timestamp)
  defp get_api_data(coin_id, timestamp, :btc), do: @cmc_client.get_data_in_btc(coin_id, timestamp)

  def extract_last_timestamp(data) do
    data
    |> Enum.take(-1)
    |> Enum.map(&(&1["timestamp"]))
    |> Enum.map(&convert_iso8601_to_system_time_seconds/1)
    |> case do
      [] -> 0
      [timestamp] -> timestamp
    end
  end
end
