defmodule Tasks.DataTransformation do
  require Logger
  @indicator_api Api.IndicatorApi
  @data_service FinanceIndicatorDaemon.DataService
  alias FinanceIndicatorDaemon.RabbitMQ.Publisher

  def add_indicator_values do
    Logger.info("📊 Starting indicator value calculation process")
    start_time = System.monotonic_time(:millisecond)

    [:usd, :btc]
    |> Enum.each(fn currency ->
      Logger.info("🔄 Processing #{String.upcase(to_string(currency))} indicator data")
      process_currency_indicator_data(currency)
    end)

    processing_time = System.monotonic_time(:millisecond) - start_time
    Logger.info("✅ Completed indicator value calculation in #{processing_time}ms")

    Logger.info("🚀 Publishing create_statistics message")
    Publisher.publish_create_statistics()
  end

  defp process_currency_indicator_data(currency) do
    file_names = @data_service.read_all_coins_data_file_names(currency)
    total_files = length(file_names)
    Logger.info("📁 Found #{total_files} #{String.upcase(to_string(currency))} data files to process")

    file_names
    |> Enum.with_index(1)
    |> Enum.each(fn {file_name, index} ->
      Logger.info("🔄 Processing file #{index}/#{total_files}: #{file_name}")
      start_time = System.monotonic_time(:millisecond)

      data = @data_service.read_coin_data(file_name, currency)
      data_points = length(data)
      Logger.info("📊 Loaded #{data_points} data points from #{file_name}")

      case @indicator_api.get_data_with_indicator_data(data) do
        {:ok, body} ->
          @data_service.write_coin_indicator_data(body, file_name, currency)
          processing_time = System.monotonic_time(:millisecond) - start_time
          Logger.info("✅ Processed #{file_name} in #{processing_time}ms")
        {:error, reason} ->
          processing_time = System.monotonic_time(:millisecond) - start_time
          Logger.error("❌ Failed to process #{file_name} after #{processing_time}ms: #{inspect(reason)}")
      end
    end)
  end

  def create_statistics() do
    Logger.info("📈 Starting statistics creation process")

    if @data_service.do_mappings_exist() do
      Logger.info("✅ Mappings found, proceeding with statistics creation")
      mappings = @data_service.read_mappings()
      mapping_count = length(mappings["data"])
      Logger.info("🗺️  Loaded #{mapping_count} cryptocurrency mappings")

      start_time = System.monotonic_time(:millisecond)

      [:usd, :btc]
      |> Enum.each(fn currency ->
        Logger.info("🔄 Creating #{String.upcase(to_string(currency))} statistics")
        process_currency_statistics(currency, mappings)
      end)

      processing_time = System.monotonic_time(:millisecond) - start_time
      Logger.info("✅ Completed statistics creation in #{processing_time}ms")
    else
      Logger.error("❌ Mappings not found, cannot create statistics")
    end
  end

  defp process_currency_statistics(currency, mappings) do
    @data_service.read_all_coins_indicator_data_file_names(currency)
    |> process_indicator_data(
      &@data_service.read_coin_indicator_data(&1, &2, currency),
      &@data_service.write_statistics(&1, &2, &3, currency),
      mappings
    )
  end

  defp process_indicator_data(file_names, read_indicator_data_fun, write_statistics_fun, mappings) do
    file_names
    |> Enum.map(&String.split(&1, "-"))
    |> Enum.group_by(&Enum.at(&1, 1))
    |> Enum.map(&latest_file_for_group(&1))
    |> Enum.map(&build_indicator_metadata(&1, mappings))
    |> Enum.map(&hydrate_with_latest_indicator_data(&1, read_indicator_data_fun))
    |> Enum.map(&persist_statistics(&1, write_statistics_fun))
  end

  defp latest_file_for_group({group, files}) do
    {group, Enum.sort(files, &(Enum.at(&1, 2) > Enum.at(&2, 2)))}
    |> elem(1)
    |> List.first()
  end

  defp build_indicator_metadata(file, mappings) do
    mapping = Enum.find(mappings["data"], &(&1["id"] == String.to_integer(Enum.at(file, 1))))
    %{
      coin_id: Enum.at(file, 1),
      date_string: String.replace(Enum.at(file, 2), ".json", ""),
      symbol: mapping["symbol"],
      slug: mapping["slug"],
      rank: mapping["rank"],
    }
  end

  defp hydrate_with_latest_indicator_data(metadata, read_indicator_data_fun) do
    latest_data =
      read_indicator_data_fun.(metadata.coin_id, metadata.date_string)
      |> Enum.sort(&(&1["timestamp"] < &2["timestamp"]))
      |> Enum.take(-1)

    Map.put(metadata, :latest_indicator_data, latest_data)
  end

  defp persist_statistics(metadata, write_statistics_fun) do
    data_to_persist = Map.delete(metadata, :coin_id)
    write_statistics_fun.(data_to_persist, metadata.coin_id, metadata.date_string)
  end
end
