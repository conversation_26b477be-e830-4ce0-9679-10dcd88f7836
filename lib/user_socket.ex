defmodule FinanceIndicatorDaemon.UserSocket do
  use Phoenix.Socket

  # Channels
  # channel "room:*", FinanceIndicatorDaemon.RoomChannel

  # Socket options
  # transport :websocket, Phoenix.Transports.WebSocket
  # transport :longpoll, Phoenix.Transports.LongPoll

  # Interceptors
  # intercept ["custom_event"]

  def connect(_params, socket, _connect_info) do
    {:ok, socket}
  end

  def id(_socket), do: nil
end
