defmodule FinanceIndicatorDaemon.MixProject do
  use Mix.Project

  def project do
    [
      app: :finance_indicator_daemon,
      version: "0.1.0",
      elixir: "~> 1.15",
      start_permanent: Mix.env() == :prod,
      deps: deps()
    ]
  end

  def application do
    [
      mod: {FinanceIndicatorDaemon, []},
      extra_applications: [:logger, :runtime_tools, :plug_cowboy, :phoenix, :hackney, :ssl, :inets]
    ]
  end

  defp deps do
    [
      {:httpoison, "~> 2.0"},
      {:jason, "~> 1.4"},
      {:poison, "~> 6.0"},
      {:dotenv, "~> 3.0.0"},
      {:timex, "~> 3.7.11"},
      {:phoenix, "~> 1.6"},
      {:phoenix_pubsub, "~> 2.0"},
      {:cowboy, "~> 2.8"},
      {:plug_cowboy, "~> 2.7.3"},
      {:amqp, "~> 4.0"},
      {:open_api_spex, "~> 3.21"}
    ]
  end
end
