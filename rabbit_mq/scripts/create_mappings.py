import json
import pika

# Define your RabbitMQ server connection parameters
rabbitmq_host = 'localhost'
rabbitmq_user = 'guest'
rabbitmq_pass = 'guest'

# Set up the connection parameters based on the credentials
credentials = pika.PlainCredentials(rabbitmq_user, rabbitmq_pass)
parameters = pika.ConnectionParameters(host=rabbitmq_host, credentials=credentials)

# Establish a connection with RabbitMQ server
connection = pika.BlockingConnection(parameters)
channel = connection.channel()

# Define the queue name
queue_name = 'create_mappings'

message = json.dumps({})

# Publish the message to the queue
channel.basic_publish(exchange='',
                      routing_key=queue_name,
                      body=message,
                      properties=pika.BasicProperties(
                          delivery_mode=2,  # Make message persistent
                      ))

print(f"Sent '{message}' to queue '{queue_name}'")

# Close the connection
connection.close()
