import json
import pika
import argparse
import os
import sys

# Define your RabbitMQ server connection parameters
rabbitmq_host = os.getenv('RABBITMQ_HOST', 'localhost')
rabbitmq_user = 'guest'
rabbitmq_pass = 'guest'

# Set up the connection parameters based on the credentials
credentials = pika.PlainCredentials(rabbitmq_user, rabbitmq_pass)
parameters = pika.ConnectionParameters(host=rabbitmq_host, credentials=credentials)

# Establish a connection with RabbitMQ server
connection = pika.BlockingConnection(parameters)
channel = connection.channel()

# Define the queue name
queue_name = 'mine_usd_data_by_symbols'

# Create the parser
parser = argparse.ArgumentParser(description='Mine USD data for cryptocurrency symbols.')

# Add the --symbols argument (required)
parser.add_argument('--symbols', nargs='+', required=True, help='List of cryptocurrency symbols (required)')

# Parse the arguments
args = parser.parse_args()

symbols = args.symbols

if not symbols:
    print("Error: No symbols provided. Symbols must be specified via --symbols argument.")
    sys.exit(1)

# Create a message as a JSON string, you can adjust the data as needed
message = json.dumps({"symbols": symbols})

# Publish the message to the queue
channel.basic_publish(exchange='',
                      routing_key=queue_name,
                      body=message,
                      properties=pika.BasicProperties(
                          delivery_mode=2,  # Make message persistent
                      ))

print(f"Sent '{message}' to queue '{queue_name}'")

# Close the connection
connection.close()
