#!/bin/bash
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
VENV_DIR="$SCRIPT_DIR/venv"

# Require symbols to be passed as arguments
if [ $# -eq 0 ]; then
    echo "Error: No symbols provided. Usage: $0 SYMBOL1 SYMBOL2 ..."
    echo "Example: $0 BTC ETH ADA SOL DOT"
    exit 1
fi

SYMBOLS=("$@")

[ ! -d "$VENV_DIR" ] && python3 -m venv "$VENV_DIR"
source "$VENV_DIR/bin/activate"
[ ! -f "$VENV_DIR/lib/python*/site-packages/pika/__init__.py" ] && pip install -r "$SCRIPT_DIR/requirements.txt"

python "$SCRIPT_DIR/create_mappings.py"
python "$SCRIPT_DIR/mine_usd_data_by_symbols.py" --symbols "${SYMBOLS[@]}"
python "$SCRIPT_DIR/mine_btc_data_by_symbols.py" --symbols "${SYMBOLS[@]}"
python "$SCRIPT_DIR/create_indicator_data.py"
python "$SCRIPT_DIR/create_statistics.py"
