package com.example.financialindicatordaemon.amqp.listener;

import com.example.financialindicatordaemon.config.RabbitMQConfig;
import com.example.financialindicatordaemon.service.DataMiningService;
import com.example.financialindicatordaemon.service.DataTransformationService;
import com.rabbitmq.client.Channel;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class CreateMappingsListener extends BaseRabbitMQListener {

    public CreateMappingsListener(DataMiningService dataMiningService,
                                  DataTransformationService dataTransformationService) {
        super(dataMiningService, dataTransformationService);
    }

    @RabbitListener(queues = RabbitMQConfig.CREATE_MAPPINGS)
    public void handleCreateMappings(@Payload Map<String, Object> ignoredMessage,
                                     Channel channel,
                                     @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
                                     Message amqpMessage) {
        handleSimpleMessage("create_mappings", channel, deliveryTag, amqpMessage,
                dataMiningService::mineMappings);
    }
}
