package com.example.financialindicatordaemon.amqp.listener;

import com.example.financialindicatordaemon.config.RabbitMQConfig;
import com.example.financialindicatordaemon.dto.SymbolsMessage;
import com.example.financialindicatordaemon.service.DataMiningService;
import com.example.financialindicatordaemon.service.DataTransformationService;
import com.rabbitmq.client.Channel;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

@Component
public class MineBtcDataListener extends BaseRabbitMQListener {

    public MineBtcDataListener(DataMiningService dataMiningService,
                               DataTransformationService dataTransformationService) {
        super(dataMiningService, dataTransformationService);
    }

    @RabbitListener(queues = RabbitMQConfig.MINE_BTC_DATA_BY_SYMBOLS)
    public void handleMineBtcDataBySymbols(@Payload SymbolsMessage message,
                                           Channel channel,
                                           @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
                                           Message amqpMessage) {
        handleSymbolsMessage(message, "btc", channel, deliveryTag, amqpMessage);
    }
}

