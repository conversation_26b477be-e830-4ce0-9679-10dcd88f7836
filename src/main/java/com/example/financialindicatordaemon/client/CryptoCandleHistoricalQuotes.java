package com.example.financialindicatordaemon.client;

import java.util.List;

public class CryptoCandleHistoricalQuotes {
    private List<CryptoCandleHistoricalQuote> quotes;

    public CryptoCandleHistoricalQuotes() {
    }

    public CryptoCandleHistoricalQuotes(List<CryptoCandleHistoricalQuote> quotes) {
        this.quotes = quotes;
    }

    public List<CryptoCandleHistoricalQuote> getQuotes() {
        return quotes;
    }

    public void setQuotes(List<CryptoCandleHistoricalQuote> quotes) {
        this.quotes = quotes;
    }

}
