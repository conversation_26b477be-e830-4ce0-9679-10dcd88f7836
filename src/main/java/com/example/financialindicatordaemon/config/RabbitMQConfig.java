package com.example.financialindicatordaemon.config;

import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.QueueBuilder;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RabbitMQConfig {

    // Queue names
    public static final String MINE_USD_DATA_BY_SYMBOLS = "mine_usd_data_by_symbols";
    public static final String MINE_BTC_DATA_BY_SYMBOLS = "mine_btc_data_by_symbols";
    public static final String CREATE_INDICATOR_DATA = "create_indicator_data";
    public static final String CREATE_STATISTICS = "create_statistics";
    public static final String CREATE_MAPPINGS = "create_mappings";

    // Queue declarations
    @Bean
    public Queue mineUsdDataBySymbolsQueue() {
        return QueueBuilder.durable(MINE_USD_DATA_BY_SYMBOLS).build();
    }

    @Bean
    public Queue mineBtcDataBySymbolsQueue() {
        return QueueBuilder.durable(MINE_BTC_DATA_BY_SYMBOLS).build();
    }

    @Bean
    public Queue createIndicatorDataQueue() {
        return QueueBuilder.durable(CREATE_INDICATOR_DATA).build();
    }

    @Bean
    public Queue createStatisticsQueue() {
        return QueueBuilder.durable(CREATE_STATISTICS).build();
    }

    @Bean
    public Queue createMappingsQueue() {
        return QueueBuilder.durable(CREATE_MAPPINGS).build();
    }

    // Message converter
    @Bean
    public Jackson2JsonMessageConverter messageConverter() {
        return new Jackson2JsonMessageConverter();
    }

    // RabbitTemplate configuration
    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setMessageConverter(messageConverter());
        return template;
    }

    // Listener container factory
    @Bean
    public SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory(ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setMessageConverter(messageConverter());
        factory.setPrefetchCount(10);
        factory.setAcknowledgeMode(org.springframework.amqp.core.AcknowledgeMode.MANUAL);
        return factory;
    }

}
