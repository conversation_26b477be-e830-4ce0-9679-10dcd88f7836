package com.example.financialindicatordaemon.controller;

import com.example.financialindicatordaemon.dto.CryptoStatisticsResponse;
import com.example.financialindicatordaemon.service.StatisticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1")
@Tag(name = "Statistics", description = "Cryptocurrency statistics API")
public class StatisticsController {

    private final StatisticsService statisticsService;

    public StatisticsController(StatisticsService statisticsService) {
        this.statisticsService = statisticsService;
    }

    @GetMapping("/crypto/statistics")
    @Operation(
            summary = "Get cryptocurrency statistics",
            description = "Retrieves cryptocurrency statistics for both USD and BTC currencies"
    )
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Successfully retrieved cryptocurrency statistics",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = CryptoStatisticsResponse.class)
                    )
            ),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(mediaType = "application/json")
            )
    })
    public ResponseEntity<CryptoStatisticsResponse> getCryptoStatistics() {
        CryptoStatisticsResponse response = statisticsService.getCryptoStatistics();
        return ResponseEntity.ok(response);
    }

}
