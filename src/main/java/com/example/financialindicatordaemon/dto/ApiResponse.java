package com.example.financialindicatordaemon.dto;

import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;

public class ApiResponse<T> {

    private final T data;
    private final HttpStatusCode status;
    private final String error;

    private ApiResponse(T data, HttpStatusCode status, String error) {
        this.data = data;
        this.status = status;
        this.error = error;
    }

    public static <T> ApiResponse<T> success(T data, HttpStatusCode status) {
        return new ApiResponse<>(data, status, null);
    }

    public static <T> ApiResponse<T> error(HttpStatusCode status, String error) {
        return new ApiResponse<>(null, status, error);
    }

    public T getData() { return data; }
    public HttpStatusCode getStatus() { return status; }
    public String getError() { return error; }
    public boolean isSuccess() { return status.is2xxSuccessful() && error == null; }
}
