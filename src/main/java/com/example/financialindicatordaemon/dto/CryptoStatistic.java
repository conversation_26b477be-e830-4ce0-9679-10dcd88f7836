package com.example.financialindicatordaemon.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import java.util.Map;

@Schema(description = "Cryptocurrency statistic data")
public class CryptoStatistic {

    @Schema(description = "Date string for the statistic", example = "2024-01-15")
    private String dateString;

    @Schema(description = "Latest indicator data for the cryptocurrency")
    private List<Map<String, Object>> latestIndicatorData;

    @Schema(description = "Ranking of the cryptocurrency", example = "1")
    private Integer rank;

    @Schema(description = "Slug identifier for the cryptocurrency", example = "bitcoin")
    private String slug;

    @Schema(description = "Symbol of the cryptocurrency", example = "BTC")
    private String symbol;

    public CryptoStatistic() {
    }

    public CryptoStatistic(String dateString, List<Map<String, Object>> latestIndicatorData,
                           Integer rank, String slug, String symbol) {
        this.dateString = dateString;
        this.latestIndicatorData = latestIndicatorData;
        this.rank = rank;
        this.slug = slug;
        this.symbol = symbol;
    }

    // Getters and Setters
    public String getDateString() {
        return dateString;
    }

    public void setDateString(String dateString) {
        this.dateString = dateString;
    }

    public List<Map<String, Object>> getLatestIndicatorData() {
        return latestIndicatorData;
    }

    public void setLatestIndicatorData(List<Map<String, Object>> latestIndicatorData) {
        this.latestIndicatorData = latestIndicatorData;
    }

    public Integer getRank() {
        return rank;
    }

    public void setRank(Integer rank) {
        this.rank = rank;
    }

    public String getSlug() {
        return slug;
    }

    public void setSlug(String slug) {
        this.slug = slug;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

}
