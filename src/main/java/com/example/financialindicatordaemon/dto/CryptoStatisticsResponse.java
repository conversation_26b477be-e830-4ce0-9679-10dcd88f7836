package com.example.financialindicatordaemon.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Response containing cryptocurrency statistics for USD and BTC")
public class CryptoStatisticsResponse {

    @Schema(description = "USD-based cryptocurrency statistics")
    private List<CryptoStatistic> usd;

    @Schema(description = "BTC-based cryptocurrency statistics")
    private List<CryptoStatistic> btc;

    public CryptoStatisticsResponse() {
    }

    public CryptoStatisticsResponse(List<CryptoStatistic> usd, List<CryptoStatistic> btc) {
        this.usd = usd;
        this.btc = btc;
    }

    // Getters and Setters
    public List<CryptoStatistic> getUsd() {
        return usd;
    }

    public void setUsd(List<CryptoStatistic> usd) {
        this.usd = usd;
    }

    public List<CryptoStatistic> getBtc() {
        return btc;
    }

    public void setBtc(List<CryptoStatistic> btc) {
        this.btc = btc;
    }

}
