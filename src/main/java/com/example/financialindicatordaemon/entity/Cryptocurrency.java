package com.example.financialindicatordaemon.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
import java.util.Map;

public class Cryptocurrency {

    private Integer cryptoCurrencyId;

    private String symbol;

    private String name;

    private String slug;

    private Integer rank;

    private Boolean isActive;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime firstHistoricalData;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastHistoricalData;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, String> platform;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    // Constructors
    public Cryptocurrency() {
    }

    public Cryptocurrency(Integer id, String symbol, String name, String slug) {
        this.cryptoCurrencyId = id;
        this.symbol = symbol;
        this.name = name;
        this.slug = slug;
    }

    // Getters and Setters
    public Integer getCryptoCurrencyId() {
        return cryptoCurrencyId;
    }

    public void setCryptoCurrencyId(Integer cryptoCurrencyId) {
        this.cryptoCurrencyId = cryptoCurrencyId;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSlug() {
        return slug;
    }

    public void setSlug(String slug) {
        this.slug = slug;
    }

    public Integer getRank() {
        return rank;
    }

    public void setRank(Integer rank) {
        this.rank = rank;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public LocalDateTime getFirstHistoricalData() {
        return firstHistoricalData;
    }

    public void setFirstHistoricalData(LocalDateTime firstHistoricalData) {
        this.firstHistoricalData = firstHistoricalData;
    }

    public LocalDateTime getLastHistoricalData() {
        return lastHistoricalData;
    }

    public void setLastHistoricalData(LocalDateTime lastHistoricalData) {
        this.lastHistoricalData = lastHistoricalData;
    }

    public Map<String, String> getPlatform() {
        return platform;
    }

    public void setPlatform(Map<String, String> platform) {
        this.platform = platform;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

}
