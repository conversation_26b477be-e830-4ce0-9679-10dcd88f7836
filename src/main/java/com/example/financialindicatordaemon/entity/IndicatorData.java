package com.example.financialindicatordaemon.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
import java.util.Map;

@TableName(value = "crypto_data.indicator_data")
public class IndicatorData {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Integer cryptocurrencyId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;

    private String currencyType;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> indicatorValues;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    // Constructors
    public IndicatorData() {
    }

    public IndicatorData(Integer cryptocurrencyId, LocalDateTime timestamp, String currencyType) {
        this.cryptocurrencyId = cryptocurrencyId;
        this.timestamp = timestamp;
        this.currencyType = currencyType;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getCryptocurrencyId() {
        return cryptocurrencyId;
    }

    public void setCryptocurrencyId(Integer cryptocurrencyId) {
        this.cryptocurrencyId = cryptocurrencyId;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public String getCurrencyType() {
        return currencyType;
    }

    public void setCurrencyType(String currencyType) {
        this.currencyType = currencyType;
    }

    public Map<String, Object> getIndicatorValues() {
        return indicatorValues;
    }

    public void setIndicatorValues(Map<String, Object> indicatorValues) {
        this.indicatorValues = indicatorValues;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

}
