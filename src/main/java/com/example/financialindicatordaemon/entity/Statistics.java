package com.example.financialindicatordaemon.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;

@TableName(value = "crypto_data.statistics")
public class Statistics {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Integer cryptocurrencyId;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate dateCalculated;

    private String currencyType;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> statisticsData;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    // Constructors
    public Statistics() {
    }

    public Statistics(Integer cryptocurrencyId, LocalDate dateCalculated, String currencyType) {
        this.cryptocurrencyId = cryptocurrencyId;
        this.dateCalculated = dateCalculated;
        this.currencyType = currencyType;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getCryptocurrencyId() {
        return cryptocurrencyId;
    }

    public void setCryptocurrencyId(Integer cryptocurrencyId) {
        this.cryptocurrencyId = cryptocurrencyId;
    }

    public LocalDate getDateCalculated() {
        return dateCalculated;
    }

    public void setDateCalculated(LocalDate dateCalculated) {
        this.dateCalculated = dateCalculated;
    }

    public String getCurrencyType() {
        return currencyType;
    }

    public void setCurrencyType(String currencyType) {
        this.currencyType = currencyType;
    }

    public Map<String, Object> getStatisticsData() {
        return statisticsData;
    }

    public void setStatisticsData(Map<String, Object> statisticsData) {
        this.statisticsData = statisticsData;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

}
