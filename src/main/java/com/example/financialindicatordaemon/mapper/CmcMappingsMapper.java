package com.example.financialindicatordaemon.mapper;

import com.example.financialindicatordaemon.client.CryptocurrencyMapping;
import com.example.financialindicatordaemon.entity.Cryptocurrency;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Optional;

@Mapper
public interface CmcMappingsMapper {

    Optional<CryptocurrencyMapping> findBySymbol(@Param("symbol") String symbol);

    void insert(@Param("mappings") List<CryptocurrencyMapping> mappings);

    Cryptocurrency findById(@Param("cryptocurrencyId") Integer cryptocurrencyId);

}
