package com.example.financialindicatordaemon.mapper;

import com.example.financialindicatordaemon.entity.IndicatorData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IndicatorDataMapper {

    List<IndicatorData> findLatestByCoinAndCurrency(@Param("coinId") Integer coinId,
                                                    @Param("currencyType") String currencyType);

    List<Integer> findDistinctCryptocurrencyIds(@Param("currencyType") String currencyType);

}
