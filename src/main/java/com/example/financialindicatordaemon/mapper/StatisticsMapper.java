package com.example.financialindicatordaemon.mapper;

import com.example.financialindicatordaemon.entity.Statistics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

@Mapper
public interface StatisticsMapper {

    List<Statistics> findByCurrencyType(@Param("currencyType") String currencyType);

    List<Statistics> findByCoinAndCurrency(@Param("coinId") Integer coinId,
                                           @Param("currencyType") String currencyType);

    Statistics findByCoinDateAndCurrency(@Param("coinId") Integer coinId,
                                         @Param("date") LocalDate date,
                                         @Param("currencyType") String currencyType);

}
