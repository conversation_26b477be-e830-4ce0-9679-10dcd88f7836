package com.example.financialindicatordaemon.service;

import com.example.financialindicatordaemon.client.CryptoCandleHistoricalQuote;
import com.example.financialindicatordaemon.mapper.CmcCandleDataMapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CmcCandleDataService {

    private CmcCandleDataMapper cmcCandleDataMapper;

    public void insert(String cryptoCurrencySymbol,
                       String conversionCurrency,
                       List<CryptoCandleHistoricalQuote> quotes) {
        cmcCandleDataMapper.insert(cryptoCurrencySymbol, conversionCurrency, quotes);
    }

}
