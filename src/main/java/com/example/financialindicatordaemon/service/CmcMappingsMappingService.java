package com.example.financialindicatordaemon.service;

import com.example.financialindicatordaemon.client.CryptocurrencyMapping;
import com.example.financialindicatordaemon.mapper.CmcMappingsMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

import static org.apache.commons.collections4.ListUtils.partition;

@Service
public class CmcMappingsMappingService {

    private final CmcMappingsMapper cmcMappingsMapper;

    public CmcMappingsMappingService(CmcMappingsMapper cmcMappingsMapper) {
        this.cmcMappingsMapper = cmcMappingsMapper;
    }

    public boolean doMappingsExist() {
        return findBySymbol("BTC").isPresent();
    }

    public Optional<CryptocurrencyMapping> findBySymbol(String symbol) {
        return cmcMappingsMapper.findBySymbol(symbol);
    }

    @Transactional
    public void insert(List<CryptocurrencyMapping> mappings) {
        partition(mappings, 5000).forEach(cmcMappingsMapper::insert);
    }

}
