package com.example.financialindicatordaemon.service;

import com.example.financialindicatordaemon.client.CoinMarketCapApiClient;
import com.example.financialindicatordaemon.client.CryptoCandleHistoricalQuotes;
import com.example.financialindicatordaemon.client.CryptocurrencyMapping;
import com.example.financialindicatordaemon.client.CryptocurrencyMappings;
import com.example.financialindicatordaemon.config.AppConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CoinMarketCapService {

    private static final Logger logger = LoggerFactory.getLogger(CoinMarketCapService.class);

    private final CoinMarketCapApiClient coinMarketCapApiClient;
    private final AppConfig appConfig;

    public CoinMarketCapService(@SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
                                CoinMarketCapApiClient coinMarketCapApiClient,
                                AppConfig appConfig) {
        this.coinMarketCapApiClient = coinMarketCapApiClient;
        this.appConfig = appConfig;
    }


    public CryptoCandleHistoricalQuotes findHistoricalQuotes(Integer coinId, Integer convertId,
                                                             String timeStart, String timeEnd) {
        return coinMarketCapApiClient.getHistoricalQuotes(
                String.valueOf(coinId),
                timeStart,
                timeEnd,
                String.valueOf(convertId),
                "daily"
        ).getBody().getData();
    }

    public List<CryptocurrencyMapping> getCryptocurrencyMappings() {
        logger.info("Retrieving all cryptocurrency mappings");
        ResponseEntity<CryptocurrencyMappings> response = coinMarketCapApiClient.getCryptocurrencyMappings(
                appConfig.cmc().apiKey());

        if (!response.getStatusCode().is2xxSuccessful()) {
            throw new RuntimeException("Failed to retrieve mappings: " + response.getStatusCode());
        }

        return response.getBody().data();
    }

}
