package com.example.financialindicatordaemon.service;

import com.example.financialindicatordaemon.client.CryptoCandleHistoricalQuote;
import com.example.financialindicatordaemon.client.CryptoCandleHistoricalQuotes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class DataMiningService {

    private static final Logger logger = LoggerFactory.getLogger(DataMiningService.class);

    private final CoinMarketCapService coinMarketCapService;
    private final CmcMappingsMappingService cmcMappingsMappingService;
    private final UnixTimestampService unixTimestampService;
    private final ;

    public DataMiningService(CoinMarketCapService coinMarketCapService, CmcMappingsMappingService cmcMappingsMappingService, UnixTimestampService unixTimestampService) {
        this.coinMarketCapService = coinMarketCapService;
        this.cmcMappingsMappingService = cmcMappingsMappingService;
        this.unixTimestampService = unixTimestampService;
    }

    public void mineMappings() {
        logger.info("Starting cryptocurrency mappings retrieval");
        if (cmcMappingsMappingService.doMappingsExist()) {
            logger.info("Mappings already exist, skipping download");
            return;
        }

        cmcMappingsMappingService.insert(coinMarketCapService.getCryptocurrencyMappings());
        logger.info("Completed cryptocurrency mappings retrieval");
    }

    public void mineSymbols(List<String> symbols, Integer currencyId) {
        symbols.stream()
                .map(cmcMappingsMappingService::findBySymbol)
                .filter(Optional::isPresent)
                .map(id -> id.get().id())
                .forEach(id -> {
                    long timeEnd = unixTimestampService.getCurrentUnixTimestamp();
                    List<CryptoCandleHistoricalQuote> collector = new ArrayList<>();
                    while (true) {
                        CryptoCandleHistoricalQuotes historicalQuotes = coinMarketCapService.findHistoricalQuotes(id, currencyId, String.valueOf(timeEnd - 15552000), String.valueOf(timeEnd));
                        List<CryptoCandleHistoricalQuote> quotes = historicalQuotes.getQuotes();
                        if (quotes.isEmpty()) {
                            break;
                        }

                        collector.addAll(quotes);
                        timeEnd -= 15552000;
                    }

                });
    }

}
