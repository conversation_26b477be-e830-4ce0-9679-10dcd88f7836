package com.example.financialindicatordaemon.service;

import com.example.financialindicatordaemon.amqp.publisher.RabbitMQPublisher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

@Service
public class DataTransformationService {

    private static final Logger logger = LoggerFactory.getLogger(DataTransformationService.class);

    private final CmcMappingsMappingService cmcMappingsMappingService;
    private final RabbitMQPublisher rabbitMQPublisher;
    private final IndicatorApiService indicatorApiService;

    public DataTransformationService(CmcMappingsMappingService cmcMappingsMappingService,
                                     RabbitMQPublisher rabbitMQPublisher,
                                     IndicatorApiService indicatorApiService) {
        this.cmcMappingsMappingService = cmcMappingsMappingService;
        this.rabbitMQPublisher = rabbitMQPublisher;
        this.indicatorApiService = indicatorApiService;
    }

    public void addIndicatorValues() {
        logger.info("📊 Starting indicator value calculation process");
        long startTime = System.currentTimeMillis();

        try {
            List<String> currencies = Arrays.asList("usd", "btc");

            for (String currency : currencies) {
                logger.info("🔄 Processing {} indicator data", currency.toUpperCase());
                processCurrencyIndicatorData(currency);
            }

            long processingTime = System.currentTimeMillis() - startTime;
            logger.info("✅ Completed indicator value calculation in {}ms", processingTime);

            logger.info("🚀 Publishing create_statistics message");
            rabbitMQPublisher.publishCreateStatistics();

        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            logger.error("❌ Failed to add indicator values after {}ms", processingTime, e);
            throw new RuntimeException("Failed to add indicator values", e);
        }
    }

    private void processCurrencyIndicatorData(String currency) {
        try {
//            List<String> fileNames = cryptocurrencyMappingService.readAllCoinsDataFileNames(currency);
//            logger.info("📁 Found {} data files for currency: {}", fileNames.size(), currency);
//
//            for (String fileName : fileNames) {
//                processIndicatorDataFile(fileName, currency);
//            }

        } catch (Exception e) {
            logger.error("❌ Failed to process currency indicator data for: {}", currency, e);
            throw new RuntimeException("Failed to process currency indicator data", e);
        }
    }

    private void processIndicatorDataFile(String fileName, String currency) {
        logger.debug("📄 Processing indicator data file: {}", fileName);
        long startTime = System.currentTimeMillis();

        try {
            // TODO: Implement reading coin data from database
            // TODO: Call indicator API to calculate indicators
            // TODO: Store indicator data back to database

            // For now, just log the processing
            logger.debug("📊 Would read coin data for file: {}", fileName);
            logger.debug("🧮 Would calculate indicators using external API");
            logger.debug("💾 Would store indicator data in database");

            long processingTime = System.currentTimeMillis() - startTime;
            logger.debug("✅ Processed {} in {}ms", fileName, processingTime);

        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            logger.error("❌ Failed to process {} after {}ms", fileName, processingTime, e);
            throw new RuntimeException("Failed to process indicator data file", e);
        }
    }

    public void createStatistics() {
        logger.info("📈 Starting statistics creation process");
        long startTime = System.currentTimeMillis();

        try {
            if (!cmcMappingsMappingService.doMappingsExist()) {
                logger.error("❌ Mappings not found, cannot create statistics");
                throw new RuntimeException("Mappings not found");
            }

            logger.info("✅ Mappings found, proceeding with statistics creation");
            var mappings = new HashMap<>();;
            @SuppressWarnings("unchecked")
            List<Object> mappingData = (List<Object>) mappings.get("data");
            int mappingCount = mappingData != null ? mappingData.size() : 0;
            logger.info("🗺️ Loaded {} cryptocurrency mappings", mappingCount);

            List<String> currencies = Arrays.asList("usd", "btc");

            for (String currency : currencies) {
                logger.info("🔄 Creating {} statistics", currency.toUpperCase());
                processCurrencyStatistics(currency, mappings);
            }

            long processingTime = System.currentTimeMillis() - startTime;
            logger.info("✅ Completed statistics creation in {}ms", processingTime);

        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            logger.error("❌ Failed to create statistics after {}ms", processingTime, e);
            throw new RuntimeException("Failed to create statistics", e);
        }
    }

    private void processCurrencyStatistics(String currency, Object mappings) {
        try {
            // TODO: Implement statistics calculation logic
            // This would involve reading indicator data and creating aggregated statistics

            logger.debug("📊 Would read indicator data for currency: {}", currency);
            logger.debug("🧮 Would calculate statistics from indicator data");
            logger.debug("💾 Would store statistics in database");

        } catch (Exception e) {
            logger.error("❌ Failed to process currency statistics for: {}", currency, e);
            throw new RuntimeException("Failed to process currency statistics", e);
        }
    }

}
