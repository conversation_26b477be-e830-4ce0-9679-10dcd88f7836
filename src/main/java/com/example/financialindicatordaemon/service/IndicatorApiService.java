package com.example.financialindicatordaemon.service;

import com.example.financialindicatordaemon.client.IndicatorApiClient;
import com.example.financialindicatordaemon.dto.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

@Service
public class IndicatorApiService {

    private static final Logger logger = LoggerFactory.getLogger(IndicatorApiService.class);

    private final IndicatorApiClient indicatorApiClient;

    public IndicatorApiService(IndicatorApiClient indicatorApiClient) {
        this.indicatorApiClient = indicatorApiClient;
    }

    public ApiResponse<String> checkHealth() {
        logger.info("Checking Indicator API health");

        try {
            ResponseEntity<String> response = indicatorApiClient.checkHealth();

            if (!response.getStatusCode().is2xxSuccessful()) {
                return ApiResponse.error(response.getStatusCode(), "Health check failed");
            }

            return ApiResponse.success(response.getBody(), response.getStatusCode());

        } catch (Exception e) {
            logger.error("Indicator API health check failed", e);
            return ApiResponse.error(org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR, e.getMessage());
        }
    }

    public ApiResponse<String> getDataWithIndicatorData(Object data) {
        try {
            logger.info("Sending calculation request to indicator API");

            ResponseEntity<String> response = indicatorApiClient.calculateIndicators(data);

            if (!response.getStatusCode().is2xxSuccessful()) {
                return ApiResponse.error(response.getStatusCode(), "Calculation request failed");
            }

            return ApiResponse.success(response.getBody(), response.getStatusCode());

        } catch (Exception e) {
            logger.error("Indicator calculation request failed", e);
            return ApiResponse.error(org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR, e.getMessage());
        }
    }

}
