package com.example.financialindicatordaemon.service;

import com.example.financialindicatordaemon.dto.CryptoStatistic;
import com.example.financialindicatordaemon.dto.CryptoStatisticsResponse;
import com.example.financialindicatordaemon.entity.Cryptocurrency;
import com.example.financialindicatordaemon.entity.IndicatorData;
import com.example.financialindicatordaemon.entity.Statistics;
import com.example.financialindicatordaemon.mapper.CmcMappingsMapper;
import com.example.financialindicatordaemon.mapper.IndicatorDataMapper;
import com.example.financialindicatordaemon.mapper.StatisticsMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class StatisticsService {

    private static final Logger logger = LoggerFactory.getLogger(StatisticsService.class);

    private final StatisticsMapper statisticsMapper;
    private final CmcMappingsMapper cmcMappingsMapper;
    private final IndicatorDataMapper indicatorDataMapper;

    public StatisticsService(StatisticsMapper statisticsMapper,
                             CmcMappingsMapper cmcMappingsMapper,
                             IndicatorDataMapper indicatorDataMapper) {
        this.statisticsMapper = statisticsMapper;
        this.cmcMappingsMapper = cmcMappingsMapper;
        this.indicatorDataMapper = indicatorDataMapper;
    }

    public CryptoStatisticsResponse getCryptoStatistics() {
        logger.info("📊 Retrieving cryptocurrency statistics");

        try {
            List<CryptoStatistic> usdStatistics = getCurrencyStatistics("USD");
            List<CryptoStatistic> btcStatistics = getCurrencyStatistics("BTC");

            logger.info("✅ Retrieved {} USD and {} BTC statistics",
                    usdStatistics.size(), btcStatistics.size());

            return new CryptoStatisticsResponse(usdStatistics, btcStatistics);

        } catch (Exception e) {
            logger.error("❌ Failed to retrieve cryptocurrency statistics", e);
            throw new RuntimeException("Failed to retrieve cryptocurrency statistics", e);
        }
    }

    private List<CryptoStatistic> getCurrencyStatistics(String currencyType) {
        List<CryptoStatistic> result = new ArrayList<>();

        try {
            List<Statistics> statistics = statisticsMapper.findByCurrencyType(currencyType);

            for (Statistics stat : statistics) {
                Cryptocurrency crypto = cmcMappingsMapper.findById(stat.getCryptocurrencyId());
                if (crypto != null) {
                    List<IndicatorData> indicatorData = indicatorDataMapper
                            .findLatestByCoinAndCurrency(stat.getCryptocurrencyId(), currencyType);

                    List<Map<String, Object>> latestIndicatorData = new ArrayList<>();
                    for (IndicatorData indicator : indicatorData) {
                        latestIndicatorData.add(indicator.getIndicatorValues());
                    }

                    String dateString = stat.getDateCalculated() != null ?
                            stat.getDateCalculated().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) : null;

                    CryptoStatistic cryptoStat = new CryptoStatistic(
                            dateString,
                            latestIndicatorData,
                            crypto.getRank(),
                            crypto.getSlug(),
                            crypto.getSymbol()
                    );

                    result.add(cryptoStat);
                }
            }

        } catch (Exception e) {
            logger.error("❌ Failed to retrieve {} statistics", currencyType, e);
            throw new RuntimeException("Failed to retrieve currency statistics", e);
        }

        return result;
    }

}
