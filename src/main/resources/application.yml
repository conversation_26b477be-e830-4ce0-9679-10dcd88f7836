server:
  port: 6601

spring:
  application:
    name: financial-indicator-daemon

  # Database Configuration
  datasource:
    url: jdbc:postgresql://${POSTGRES_HOST:localhost}:${POSTGRES_PORT:5432}/${POSTGRES_DB:financial_indicator_db}
    username: ${POSTGRES_USER:financial_user}
    password: ${POSTGRES_PASSWORD:financial_pass}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # Liquibase Configuration
  liquibase:
    change-log: classpath:db/changelog/db.changelog-master.sql
    default-schema: public
    enabled: true

  # RabbitMQ Configuration
  rabbitmq:
    host: ${RABBITMQ_HOST:localhost}
    port: ${RABBITMQ_PORT:5672}
    username: ${RABBITMQ_USER:guest}
    password: ${RABBITMQ_PASSWORD:guest}
    listener:
      simple:
        prefetch: 10
        acknowledge-mode: manual
        retry:
          enabled: true
          initial-interval: 2s
          max-attempts: 3
    template:
      retry:
        enabled: true
        initial-interval: 2s

# MyBatis Plus Configuration
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.example.financialindicatordaemon.entity

# Application Configuration
app:
  # External API Configuration
  cmc:
    api-key: ${CMC_API_KEY:f8e302bb-197c-4a01-8fcf-136e9d334680}
    throttle:
      min: ${CMC_API_THROTTLE_MIN:300}
      max: ${CMC_API_THROTTLE_MAX:500}
    symbol-overrides: ${CMC_SYMBOL_OVERRIDES:}

  indicator-api:
    host: ${INDICATOR_API_HOST:http://localhost:5000}

  # Data Configuration
  amount-of-coins-to-get-by-ranking: ${AMOUNT_OF_COINS_TO_GET_BY_RANKING:3}

# Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

# OpenAPI Configuration
springdoc:
  api-docs:
    path: /docs/openapi.json
  swagger-ui:
    path: /docs
    operationsSorter: method

# Logging Configuration
logging:
  level:
    com.example.financialindicatordaemon: INFO
    org.springframework.amqp: INFO
    com.baomidou.mybatisplus: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
