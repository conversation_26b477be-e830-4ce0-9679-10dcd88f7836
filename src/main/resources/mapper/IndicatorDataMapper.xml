<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.financialindicatordaemon.mapper.IndicatorDataMapper">

    <select id="findLatestByCoinAndCurrency" resultType="com.example.financialindicatordaemon.entity.IndicatorData">
        SELECT *
        FROM crypto_data.indicator_data
        WHERE cryptocurrency_id = #{coinId}
          AND currency_type = #{currencyType}
        ORDER BY timestamp DESC
    </select>

    <select id="findDistinctCryptocurrencyIds" resultType="java.lang.Integer">
        SELECT DISTINCT cryptocurrency_id
        FROM crypto_data.indicator_data
        WHERE currency_type = #{currencyType}
    </select>

</mapper>
