<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.financialindicatordaemon.mapper.StatisticsMapper">

    <select id="findByCurrencyType" resultType="com.example.financialindicatordaemon.entity.Statistics">
        SELECT *
        FROM crypto_data.statistics
        WHERE currency_type = #{currencyType}
        ORDER BY date_calculated DESC, cryptocurrency_id ASC
    </select>

    <select id="findByCoinAndCurrency" resultType="com.example.financialindicatordaemon.entity.Statistics">
        SELECT *
        FROM crypto_data.statistics
        WHERE cryptocurrency_id = #{coinId}
          AND currency_type = #{currencyType}
        ORDER BY date_calculated DESC
    </select>

    <select id="findByCoinDateAndCurrency" resultType="com.example.financialindicatordaemon.entity.Statistics">
        SELECT *
        FROM crypto_data.statistics
        WHERE cryptocurrency_id = #{coinId}
          AND date_calculated = #{date}
          AND currency_type = #{currencyType}
    </select>

</mapper>
