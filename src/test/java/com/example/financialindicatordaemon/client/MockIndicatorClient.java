package com.example.financialindicatordaemon.client;

import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

@Component
public class MockIndicatorClient implements IndicatorApiClient {
    @Override
    public ResponseEntity<String> checkHealth() {
        return null;
    }

    @Override
    public ResponseEntity<String> calculateIndicators(Object data) {
        return null;
    }

}
