# Multi-stage build for Spring Boot test environment
FROM gradle:8.7-jdk17 AS build

WORKDIR /app

# Copy Gradle files first for better Docker layer caching
COPY build.gradle settings.gradle gradlew ./
COPY gradle ./gradle

# Download dependencies (cached layer)
RUN gradle dependencies --no-daemon

# Copy source code
COPY src ./src

# Compile the application (without running tests)
RUN gradle compileJava compileTestJava --no-daemon

# Runtime stage for testing
FROM eclipse-temurin:17-jdk-alpine

WORKDIR /app

# Install necessary tools
RUN apk add --no-cache curl

# Copy built application and Gradle wrapper
COPY --from=build /app .

# Create non-root user for security
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup && \
    chown -R appuser:appgroup /app

USER appuser

# Default command to run tests
CMD ["./gradlew", "test", "--info"]
