FROM elixir:1.15

# Build arguments for cache invalidation
ARG BUILD_DATE
ARG VCS_REF
ARG CODE_HASH
ARG BUILDKIT_INLINE_CACHE

# Labels for better tracking
LABEL build_date=$BUILD_DATE
LABEL vcs_ref=$VCS_REF
LABEL code_hash=$CODE_HASH
LABEL description="Financial Indicator Daemon Test Environment"

ENV MIX_ENV=test
WORKDIR /app

# Install Elixir tools (cached layer)
RUN mix local.hex --force && mix local.rebar --force

# Copy dependency files first for better caching
COPY mix.exs mix.lock* ./
RUN mix deps.get && mix deps.compile

# Copy configuration files (changes less frequently)
COPY config/ ./config/
COPY .formatter.exs ./
COPY .env ./

# Copy source code (this layer will be invalidated when code changes)
# Use CODE_HASH to invalidate cache when source code changes
RUN echo "Code hash: $CODE_HASH" > /app/code_hash.txt
COPY lib/ ./lib/
COPY test/ ./test/

# Add build metadata
RUN echo "Build date: $BUILD_DATE" > /app/build_info.txt && \
    echo "VCS ref: $VCS_REF" >> /app/build_info.txt && \
    echo "Code hash: $CODE_HASH" >> /app/build_info.txt

# Compile the application
RUN mix compile

# Create necessary test directories
RUN mkdir -p /tmp/test/{mappings,candle_data/{usd,btc},indicator_data/{usd,btc},statistics/{usd,btc}}

CMD ["mix", "test"]
