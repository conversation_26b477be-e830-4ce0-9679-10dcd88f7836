services:
  test:
    build:
      context: ..
      dockerfile: test/Dockerfile.test
      args:
        - BUILDKIT_INLINE_CACHE=1
        - BUILD_DATE=${BUILD_DATE:-$(date -u +'%Y-%m-%dT%H:%M:%SZ')}
        - VCS_REF=${VCS_REF:-$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')}
        - CODE_HASH=${CODE_HASH:-$(find ../lib ../test ../config -type f -name "*.ex*" -exec md5sum {} \; 2>/dev/null | md5sum | cut -d' ' -f1 || echo 'unknown')}
      # Remove no_cache to allow dependency layer caching
    env_file:
      - ../.env
    environment:
      MIX_ENV: test
      BASE_APP_PATH: /tmp/test
      CMC_API_THROTTLE_MIN: 300
      CMC_API_THROTTLE_MAX: 500
    networks:
      - docker-network
    volumes:
      - ".:/app/test:ro"
      - "test_output:/tmp/test"
      - "../out:/app/out"
    command: ["mix", "test"]

volumes:
  test_output:

networks:
  docker-network:
    external: true
