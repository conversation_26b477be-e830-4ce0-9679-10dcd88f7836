defmodule FinanceIndicatorDaemon.RabbitMQ.ConfigTest do
  use ExUnit.Case, async: true
  alias FinanceIndicatorDaemon.RabbitMQ.Config

  test "consumer_configs returns expected configurations" do
    configs = Config.consumer_configs()
    assert length(configs) == 5

    queue_names = Enum.map(configs, fn {queue, _type, _arg} -> queue end)
    expected_queues = ["mine_usd_data_by_symbols", "mine_btc_data_by_symbols", "create_indicator_data", "create_statistics", "create_mappings"]
    assert Enum.all?(expected_queues, &(&1 in queue_names))
  end

  test "consumer_configs returns correct processor types" do
    configs = Config.consumer_configs()
    usd_config = Enum.find(configs, fn {queue, _type, _arg} -> queue == "mine_usd_data_by_symbols" end)
    btc_config = Enum.find(configs, fn {queue, _type, _arg} -> queue == "mine_btc_data_by_symbols" end)

    assert {_queue, :symbols_processor, :usd} = usd_config
    assert {_queue, :symbols_processor, :btc} = btc_config

    simple_processors = Enum.filter(configs, fn {_queue, type, _arg} -> type == :simple_processor end)
    assert length(simple_processors) == 3
  end

  test "build_consumer_spec for symbols processor" do
    config = {"mine_usd_data_by_symbols", :symbols_processor, :usd}
    spec = Config.build_consumer_spec(config)

    assert %{id: :consumer_mine_usd_data_by_symbols, start: {FinanceIndicatorDaemon.Consumer, :start_link, [opts]}} = spec
    assert Keyword.get(opts, :queue) == "mine_usd_data_by_symbols"
    assert is_function(Keyword.get(opts, :process_function), 3)
  end

  test "build_consumer_spec for simple processor" do
    test_function = fn -> :ok end
    config = {"test_queue", :simple_processor, test_function}
    spec = Config.build_consumer_spec(config)

    assert %{id: :consumer_test_queue, start: {FinanceIndicatorDaemon.Consumer, :start_link, [opts]}} = spec
    assert Keyword.get(opts, :queue) == "test_queue"
    assert is_function(Keyword.get(opts, :process_function), 3)
  end

  test "build_process_function for symbols processor" do
    assert is_function(Config.build_process_function(:symbols_processor, :usd), 3)
    assert is_function(Config.build_process_function(:symbols_processor, :btc), 3)
  end

  test "build_process_function for simple processor" do
    test_function = fn -> :test_result end
    process_fn = Config.build_process_function(:simple_processor, test_function)
    assert is_function(process_fn, 3)
  end

  @tag :integration
  test "process_symbols_for_currency USD" do
    symbols = ["BTC", "ETH"]
    assert :ok == Config.process_symbols_for_currency(symbols, :usd)
  end

  @tag :integration
  test "process_symbols_for_currency BTC" do
    symbols = ["BTC", "ETH"]
    assert :ok == Config.process_symbols_for_currency(symbols, :btc)
  end

  test "consumer_specs returns list of supervisor child specs" do
    specs = Config.consumer_specs()
    assert is_list(specs)
    assert length(specs) == 5

    Enum.each(specs, fn spec ->
      assert Map.has_key?(spec, :id)
      assert Map.has_key?(spec, :start)
      assert {FinanceIndicatorDaemon.Consumer, :start_link, [_opts]} = spec.start
    end)
  end

  test "consumer_specs generates unique consumer IDs" do
    specs = Config.consumer_specs()
    ids = Enum.map(specs, & &1.id)
    assert length(ids) == length(Enum.uniq(ids))

    expected_ids = [:consumer_mine_usd_data_by_symbols, :consumer_mine_btc_data_by_symbols, :consumer_create_indicator_data, :consumer_create_statistics, :consumer_create_mappings]
    assert Enum.all?(expected_ids, &(&1 in ids))
  end
end
