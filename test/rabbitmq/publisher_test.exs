defmodule FinanceIndicatorDaemon.RabbitMQ.PublisherTest do
  use ExUnit.Case, async: false
  alias FinanceIndicatorDaemon.RabbitMQ.Publisher

  test "publish_create_indicator_data" do
    result = Publisher.publish_create_indicator_data()
    assert result == :ok or match?({:error, _}, result)
  end

  test "publish_create_statistics" do
    result = Publisher.publish_create_statistics()
    assert result == :ok or match?({:error, _}, result)
  end

  test "publish_message with payload" do
    result = Publisher.publish_message("test_queue", %{"test" => "data"})
    assert result == :ok or match?({:error, _}, result)
  end

  test "publish_message with empty payload" do
    result = Publisher.publish_message("test_queue", %{})
    assert result == :ok or match?({:error, _}, result)
  end

  test "publish_message with complex payload" do
    payload = %{
      "symbols" => ["BTC", "ETH"],
      "timestamp" => DateTime.utc_now() |> DateTime.to_iso8601(),
      "metadata" => %{"source" => "test"}
    }
    result = Publisher.publish_message("test_queue", payload)
    assert result == :ok or match?({:error, _}, result)
  end
end
