#!/bin/bash

# Full test runner - includes integration tests that make real API calls
# Use this when you want to run the complete test suite

set -e  # Exit on any error

echo "🧪 Running Financial Indicator Daemon Tests (FULL SUITE)"
echo "======================================================="

# Get current directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

cd "$PROJECT_ROOT"

# Generate minimal build arguments
export CODE_HASH=$(find lib test config -type f -name "*.ex*" -exec md5sum {} \; 2>/dev/null | md5sum | cut -d' ' -f1 || echo 'unknown')

echo "🔍 Code Hash: $CODE_HASH"
echo ""

# Quick container cleanup (no image removal)
docker-compose -f test/docker-compose.test.yml down --remove-orphans 2>/dev/null || true

# Fast build - maximum caching, only rebuild if code changed
echo "⚡ Fast build (maximum caching)..."
docker-compose -f test/docker-compose.test.yml build --build-arg CODE_HASH="$CODE_HASH"

echo "🧪 Running ALL tests (including integration tests)..."
echo "⚠️  Warning: Integration tests make real API calls and may take several minutes"
docker-compose -f test/docker-compose.test.yml run --rm test mix test --timeout 300000

# Capture exit code
TEST_EXIT_CODE=$?

# Quick cleanup
docker-compose -f test/docker-compose.test.yml down --remove-orphans 2>/dev/null || true

if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo "✅ All tests passed!"
else
    echo "❌ Tests failed with exit code $TEST_EXIT_CODE"
fi

exit $TEST_EXIT_CODE
