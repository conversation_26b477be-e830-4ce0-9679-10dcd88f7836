defmodule FinanceIndicatorDaemon.DataServiceTest do
  use ExUnit.Case
  doctest FinanceIndicatorDaemon.DataService

  @data_service FinanceIndicatorDaemon.DataService

  test "symbol override configuration resolves BOZO to correct coin ID" do
    # Test that <PERSON><PERSON><PERSON><PERSON> symbol resolves to the configured override ID (29308) instead of default (28991)
    # This tests the CMC_SYMBOL_OVERRIDES environment variable functionality
    
    # Ensure mappings exist for the test
    unless @data_service.do_mappings_exist() do
      Tasks.DataMining.get_cryptocurrencies_mappings()
    end
    
    coin_id = @data_service.get_coin_id("BOZO")
    
    # Should resolve to 29308 (<PERSON><PERSON>) due to CMC_SYMBOL_OVERRIDES=BOZO:29308 in .env
    # Without override, it would resolve to 28991 (BOZO) - the first match in mappings
    assert coin_id == 29308, "BOZ<PERSON> should resolve to 29308 (<PERSON><PERSON>) via symbol override configuration"
  end

  test "get_coin_id returns nil for non-existent symbol" do
    # Ensure mappings exist for the test
    unless @data_service.do_mappings_exist() do
      Tasks.DataMining.get_cryptocurrencies_mappings()
    end
    
    coin_id = @data_service.get_coin_id("NONEXISTENT_SYMBOL_12345")
    assert coin_id == nil
  end

  test "get_coin_id returns correct ID for known symbol without override" do
    # Ensure mappings exist for the test
    unless @data_service.do_mappings_exist() do
      Tasks.DataMining.get_cryptocurrencies_mappings()
    end
    
    # Test a known symbol that should work without override (Bitcoin)
    coin_id = @data_service.get_coin_id("BTC")
    assert coin_id == 1
  end
end
