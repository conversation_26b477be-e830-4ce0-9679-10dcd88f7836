defmodule Tasks.DataMiningTest do
  use ExUnit.Case
  doctest Tasks.DataMining

  @data_service FinanceIndicatorDaemon.DataService

  test "get cryptocurrencies mappings" do
    @data_service.delete_mappings()
    assert !@data_service.do_mappings_exist()
    Tasks.DataMining.get_cryptocurrencies_mappings()
    assert @data_service.do_mappings_exist()
  end

  @tag :integration
  test "get btc usd data for all time" do
    Tasks.DataMining.get_coin_usd_data_for_all_time(1)
  end

  @tag :integration
  test "get eth btc data for all time" do
    Tasks.DataMining.get_coin_btc_data_for_all_time(1027)
  end

  test "last_date_in_seconds correctly prevents fetching duplicate historical data" do
    # Test the core logic: last_date_in_seconds should be used as end_date to avoid re-fetching existing data

    # Mock data with specific timestamps
    existing_data = [
      %{"timestamp" => "2023-01-01T00:00:00Z", "price" => 100},
      %{"timestamp" => "2023-01-02T00:00:00Z", "price" => 110},
      %{"timestamp" => "2023-01-03T00:00:00Z", "price" => 120}
    ]

    # Test extract_last_timestamp function directly
    last_timestamp = Tasks.DataMining.extract_last_timestamp(existing_data)

    # Convert expected timestamp to verify
    {:ok, expected_datetime, _} = DateTime.from_iso8601("2023-01-03T00:00:00Z")
    expected_timestamp = DateTime.to_unix(expected_datetime, :second)

    assert last_timestamp == expected_timestamp

    # The key insight: this timestamp becomes the end_date parameter for API calls
    # When API client receives this as end_date, it should only fetch data AFTER this timestamp
    # This prevents re-fetching the data from 2023-01-01 to 2023-01-03 that we already have
  end

  test "extract_last_timestamp handles empty data correctly" do
    # When no existing data, should return 0 to fetch all historical data
    empty_data = []
    last_timestamp = Tasks.DataMining.extract_last_timestamp(empty_data)
    assert last_timestamp == 0
  end

  test "extract_last_timestamp handles single data point correctly" do
    single_data = [%{"timestamp" => "2023-06-15T12:00:00Z", "price" => 200}]
    last_timestamp = Tasks.DataMining.extract_last_timestamp(single_data)

    {:ok, expected_datetime, _} = DateTime.from_iso8601("2023-06-15T12:00:00Z")
    expected_timestamp = DateTime.to_unix(expected_datetime, :second)

    assert last_timestamp == expected_timestamp
  end
end
