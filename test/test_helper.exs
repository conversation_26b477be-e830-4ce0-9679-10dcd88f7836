ExUnit.start(
  capture_log: false
)

defmodule TestHelper do
  def test_data_dir, do: System.get_env("BASE_APP_PATH", "/tmp/test")

  def ensure_test_dirs do
    base = test_data_dir()
    ["mappings", "candle_data/usd", "candle_data/btc", "indicator_data/usd",
     "indicator_data/btc", "statistics/usd", "statistics/btc"]
    |> Enum.each(&File.mkdir_p!("#{base}/#{&1}"))
  end
end

TestHelper.ensure_test_dirs()
